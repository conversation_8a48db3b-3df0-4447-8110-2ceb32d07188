#!/usr/bin/env python3
"""
Compact Side Window Real-time Search Monitor
A small, compact window that stays on the right side of your screen.
"""

import threading
import time
import logging
import sys
import os
import subprocess
import ctypes
import webbrowser
from pathlib import Path
from typing import List, Dict, Any
import tkinter as tk
from tkinter import ttk, messagebox
import requests

# Try to import monitoring dependencies
try:
    import keyboard
    import pyperclip
    MONITORING_AVAILABLE = True
except ImportError:
    MONITORING_AVAILABLE = False

try:
    import win32gui
    import win32con
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CompactGlobalMonitor:
    """Compact global monitor for side window."""
    
    def __init__(self, callback):
        self.callback = callback
        self.is_monitoring = False
        self.current_word = ""
        
    def start_monitoring(self):
        """Start global keyboard monitoring."""
        if not MONITORING_AVAILABLE:
            return False
            
        if self.is_monitoring:
            return True
            
        try:
            self.is_monitoring = True
            keyboard.on_press(self._on_key_press)
            logger.info("✅ Global monitoring started")
            return True
        except Exception as e:
            logger.error(f"❌ Monitoring failed: {e}")
            self.is_monitoring = False
            return False
            
    def stop_monitoring(self):
        """Stop monitoring."""
        if MONITORING_AVAILABLE and self.is_monitoring:
            try:
                keyboard.unhook_all()
            except:
                pass
        self.is_monitoring = False
        logger.info("🛑 Monitoring stopped")
        
    def _on_key_press(self, key):
        """Handle key press events."""
        if not self.is_monitoring:
            return
            
        try:
            key_name = key.name if hasattr(key, 'name') else str(key)
            
            # Handle spacebar - clear search
            if key_name == 'space':
                if self.current_word.strip():
                    self.current_word = ""
                    self.callback("")
                return
                
            # Handle backspace
            elif key_name == 'backspace':
                if self.current_word:
                    self.current_word = self.current_word[:-1]
                    self.callback(self.current_word)
                return
                
            # Handle regular characters
            elif len(key_name) == 1 and key_name.isalnum():
                self.current_word += key_name
                self.callback(self.current_word)
                
        except Exception as e:
            logger.error(f"Key press error: {e}")

class CompactSearchAPI:
    """Compact search API client."""
    
    def __init__(self):
        self.base_url = "http://127.0.0.1:8000"
        
    def search(self, query: str) -> List[Dict[str, Any]]:
        """Search for query."""
        if not query.strip():
            return []
            
        try:
            response = requests.post(
                f"{self.base_url}/search",
                json={"query": query, "limit": 6, "similarity_threshold": 0.2},
                timeout=2
            )
            
            if response.status_code == 200:
                data = response.json()
                results = data.get("results", [])
                # Filter for better relevance
                filtered_results = [r for r in results if r.get('similarity', 0) > 0.35]
                return filtered_results if filtered_results else results[:4]
            return []
        except Exception as e:
            logger.error(f"Search error: {e}")
            return []
            
    def check_backend(self) -> bool:
        """Check if backend is running."""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=2)
            return response.status_code == 200
        except:
            return False

class CompactSideWindow:
    """Compact side window for real-time search."""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔍 Real-time Search")
        
        # Compact window settings
        self.window_width = 380
        self.window_height = 600
        
        # Position on right side of screen
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x_position = screen_width - self.window_width - 20  # 20px from right edge
        y_position = 50  # 50px from top
        
        self.root.geometry(f"{self.window_width}x{self.window_height}+{x_position}+{y_position}")
        
        # Window properties
        self.root.configure(bg='#f8f9fa')
        self.root.resizable(True, True)
        self.root.attributes('-topmost', True)  # Always on top
        
        # Components
        self.monitor = CompactGlobalMonitor(self.on_text_detected)
        self.search_api = CompactSearchAPI()
        
        # State
        self.is_monitoring = False
        self.current_query = ""
        self.search_results = []
        self.backend_running = False
        
        # Create compact GUI
        self.create_compact_widgets()
        
        # Check backend
        self.check_backend()
        
        # Bind events
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.bind('<Button-1>', self.on_click)  # Click to focus
        
    def create_compact_widgets(self):
        """Create compact GUI widgets."""
        # Main container with padding
        main_frame = ttk.Frame(self.root, padding=8)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Compact title
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 8))
        
        title_label = ttk.Label(title_frame, text="🔍 Real-time Search", 
                               font=('Segoe UI', 12, 'bold'), foreground='#2c3e50')
        title_label.pack(side=tk.LEFT)
        
        # Minimize button
        minimize_btn = ttk.Button(title_frame, text="−", width=3,
                                 command=self.toggle_minimize)
        minimize_btn.pack(side=tk.RIGHT)
        
        # Status indicators (compact)
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill=tk.X, pady=(0, 8))
        
        # Backend status
        ttk.Label(status_frame, text="Backend:", font=('Segoe UI', 8)).pack(side=tk.LEFT)
        self.backend_status = ttk.Label(status_frame, text="●", font=('Segoe UI', 8), foreground='red')
        self.backend_status.pack(side=tk.LEFT, padx=(5, 15))
        
        # Monitor status
        ttk.Label(status_frame, text="Monitor:", font=('Segoe UI', 8)).pack(side=tk.LEFT)
        self.monitor_status = ttk.Label(status_frame, text="●", font=('Segoe UI', 8), foreground='red')
        self.monitor_status.pack(side=tk.LEFT, padx=(5, 0))
        
        # Compact control buttons
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 8))
        
        self.start_backend_btn = ttk.Button(control_frame, text="Start Backend", 
                                           command=self.start_backend)
        self.start_backend_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.start_monitor_btn = ttk.Button(control_frame, text="Start Monitor", 
                                           command=self.toggle_monitoring, state="disabled")
        self.start_monitor_btn.pack(side=tk.LEFT)
        
        # Current word display (compact)
        word_frame = ttk.LabelFrame(main_frame, text="Current Word", padding=5)
        word_frame.pack(fill=tk.X, pady=(0, 8))
        
        self.current_word_display = ttk.Label(word_frame, text="(not monitoring)", 
                                             font=('Segoe UI', 11, 'bold'), foreground='gray',
                                             background='white', relief='sunken', padding=5)
        self.current_word_display.pack(fill=tk.X)
        
        # Compact instructions
        instructions_frame = ttk.Frame(main_frame)
        instructions_frame.pack(fill=tk.X, pady=(0, 8))
        
        instructions = "1. Start Backend → 2. Start Monitor → 3. Type anywhere!"
        ttk.Label(instructions_frame, text=instructions, font=('Segoe UI', 8), 
                 foreground='#666', wraplength=350).pack()
        
        # Compact results area
        results_frame = ttk.LabelFrame(main_frame, text="Search Results", padding=5)
        results_frame.pack(fill=tk.BOTH, expand=True)
        
        # Results with scrollbar
        results_container = ttk.Frame(results_frame)
        results_container.pack(fill=tk.BOTH, expand=True)
        
        self.results_text = tk.Text(results_container, font=('Segoe UI', 9), wrap=tk.WORD,
                                   height=15, selectbackground='#3498db', selectforeground='white')
        self.results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Compact scrollbar
        scrollbar = ttk.Scrollbar(results_container, orient=tk.VERTICAL, command=self.results_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.results_text.configure(yscrollcommand=scrollbar.set)
        
        # Bind double-click to copy
        self.results_text.bind('<Double-1>', self.copy_chunk_at_cursor)
        
        # Initial compact message
        self.results_text.insert(tk.END, "🚀 Start monitoring to see live search results!\n\n", "header")
        self.results_text.insert(tk.END, "💡 Quick tips:\n", "tips")
        self.results_text.insert(tk.END, "• Type in any app\n")
        self.results_text.insert(tk.END, "• Press SPACEBAR to clear\n")
        self.results_text.insert(tk.END, "• Double-click to copy\n")
        self.results_text.insert(tk.END, "• Window stays on top\n\n")
        
        # Configure text styles
        self.results_text.tag_config("header", font=('Segoe UI', 10, 'bold'), foreground='#2c3e50')
        self.results_text.tag_config("tips", font=('Segoe UI', 9, 'bold'), foreground='#3498db')
        
    def toggle_minimize(self):
        """Toggle window minimize state."""
        if self.root.state() == 'normal':
            self.root.iconify()
        else:
            self.root.deiconify()
            self.root.lift()
            
    def on_click(self, event):
        """Handle window click to bring to front."""
        self.root.lift()
        
    def check_backend(self):
        """Check backend status."""
        if self.search_api.check_backend():
            self.backend_status.config(text="●", foreground="green")
            self.backend_running = True
            self.start_backend_btn.config(text="✓ Running")
            self.start_monitor_btn.config(state="normal")
        else:
            self.backend_status.config(text="●", foreground="red")
            self.backend_running = False
            
    def start_backend(self):
        """Start backend."""
        self.start_backend_btn.config(text="Starting...", state="disabled")
        self.backend_status.config(text="●", foreground="orange")
        
        threading.Thread(target=self._start_backend_process, daemon=True).start()
        
    def _start_backend_process(self):
        """Start backend process."""
        try:
            subprocess.Popen([sys.executable, "start_backend.py"], cwd=Path(__file__).parent)
            
            for i in range(25):
                time.sleep(1)
                if self.search_api.check_backend():
                    self.root.after(0, self.on_backend_started)
                    return
                    
            self.root.after(0, self.on_backend_failed)
            
        except Exception as e:
            logger.error(f"Failed to start backend: {e}")
            self.root.after(0, self.on_backend_failed)
            
    def on_backend_started(self):
        """Backend started successfully."""
        self.backend_running = True
        self.backend_status.config(text="●", foreground="green")
        self.start_backend_btn.config(text="✓ Running", state="normal")
        self.start_monitor_btn.config(state="normal")
        
        # Update results area
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, "✅ Backend started!\n\n", "success")
        self.results_text.insert(tk.END, "Now click 'Start Monitor' to begin real-time search.\n")
        self.results_text.tag_config("success", font=('Segoe UI', 9, 'bold'), foreground='green')
        
    def on_backend_failed(self):
        """Backend failed to start."""
        self.backend_status.config(text="●", foreground="red")
        self.start_backend_btn.config(text="Start Backend", state="normal")
        messagebox.showerror("Error", "Backend failed to start.")
        
    def toggle_monitoring(self):
        """Toggle global monitoring."""
        if not MONITORING_AVAILABLE:
            messagebox.showerror("Error", 
                               "Monitoring requires:\npip install keyboard pyperclip\n\n"
                               "Run as administrator for best results.")
            return
            
        if not self.is_monitoring:
            success = self.monitor.start_monitoring()
            if success:
                self.is_monitoring = True
                self.start_monitor_btn.config(text="Stop Monitor")
                self.monitor_status.config(text="●", foreground="green")
                self.current_word_display.config(text="Ready! Type anywhere...", foreground="blue")
                
                # Update results
                self.results_text.delete(1.0, tk.END)
                self.results_text.insert(tk.END, "🌍 Global monitoring active!\n\n", "active")
                self.results_text.insert(tk.END, "Type in any application to see live search results.\n\n")
                self.results_text.insert(tk.END, "Supported apps:\n• Notepad, Word, VS Code\n• Any text editor\n")
                self.results_text.tag_config("active", font=('Segoe UI', 9, 'bold'), foreground='green')
            else:
                messagebox.showerror("Error", "Failed to start monitoring.\nTry running as administrator.")
        else:
            self.monitor.stop_monitoring()
            self.is_monitoring = False
            self.start_monitor_btn.config(text="Start Monitor")
            self.monitor_status.config(text="●", foreground="red")
            self.current_word_display.config(text="(not monitoring)", foreground="gray")
            
    def on_text_detected(self, text: str):
        """Handle text detected from monitoring."""
        self.current_query = text
        
        # Update display
        if text:
            self.current_word_display.config(text=f"'{text}'", foreground="blue")
        else:
            self.current_word_display.config(text="(cleared)", foreground="green")
            self.results_text.delete(1.0, tk.END)
            self.results_text.insert(tk.END, "Search cleared. Start typing...\n")
            return
            
        # Search
        if len(text) >= 1:
            threading.Thread(target=self._search_background, args=(text,), daemon=True).start()
            
    def _search_background(self, query: str):
        """Search in background."""
        try:
            results = self.search_api.search(query)
            self.root.after(0, lambda: self._update_results(query, results))
        except Exception as e:
            logger.error(f"Search error: {e}")
            
    def _update_results(self, query: str, results: List[Dict[str, Any]]):
        """Update search results in compact format."""
        if query != self.current_query:
            return
            
        self.search_results = results
        self.results_text.delete(1.0, tk.END)
        
        if not results:
            self.results_text.insert(tk.END, f"🔍 '{query}'\n", "query")
            self.results_text.insert(tk.END, "No results found.\n\n")
            self.results_text.insert(tk.END, "💡 Try typing more letters or different words.")
            return
            
        # Compact header
        self.results_text.insert(tk.END, f"🔍 '{query}' ", "query")
        self.results_text.insert(tk.END, f"({len(results)} found)\n\n", "count")
        
        # Compact results
        for i, result in enumerate(results, 1):
            content = result.get('content', '').strip()
            source = result.get('source', 'Unknown').split('/')[-1]
            similarity = result.get('similarity', 0) * 100
            
            # Compact result format
            self.results_text.insert(tk.END, f"{i}. ", "number")
            self.results_text.insert(tk.END, f"[{similarity:.0f}%] ", "score")
            self.results_text.insert(tk.END, f"{source}\n", "source")
            
            # Content preview (shorter for compact view)
            preview = content[:120] + "..." if len(content) > 120 else content
            chunk_start = self.results_text.index(tk.INSERT)
            self.results_text.insert(tk.END, preview, "content")
            chunk_end = self.results_text.index(tk.INSERT)
            self.results_text.insert(tk.END, "\n\n", "content")
            
            # Tag for copying
            self.results_text.tag_add(f"chunk_{i}", chunk_start, chunk_end)
            self.results_text.tag_config(f"chunk_{i}", background="#e8f4fd", relief="flat")
            
        # Compact styles
        self.results_text.tag_config("query", font=('Segoe UI', 9, 'bold'), foreground='#2c3e50')
        self.results_text.tag_config("count", font=('Segoe UI', 8), foreground='#666')
        self.results_text.tag_config("number", font=('Segoe UI', 8, 'bold'), foreground='#3498db')
        self.results_text.tag_config("score", font=('Segoe UI', 8, 'bold'), foreground='#e67e22')
        self.results_text.tag_config("source", font=('Segoe UI', 8), foreground='#666')
        self.results_text.tag_config("content", font=('Segoe UI', 9), foreground='#2c3e50')
        
        self.results_text.see(1.0)
        
    def copy_chunk_at_cursor(self, event):
        """Copy chunk at cursor position."""
        try:
            cursor_pos = self.results_text.index(tk.CURRENT)
            
            # Find which chunk was clicked
            for i, result in enumerate(self.search_results, 1):
                chunk_tag = f"chunk_{i}"
                try:
                    ranges = self.results_text.tag_ranges(chunk_tag)
                    if ranges:
                        start, end = ranges[0], ranges[1]
                        if self.results_text.compare(start, "<=", cursor_pos) and self.results_text.compare(cursor_pos, "<=", end):
                            # Copy this chunk
                            content = result.get('content', '').strip()
                            if MONITORING_AVAILABLE:
                                pyperclip.copy(content)
                            else:
                                self.root.clipboard_clear()
                                self.root.clipboard_append(content)
                            
                            # Brief confirmation
                            source = result.get('source', 'Unknown').split('/')[-1]
                            similarity = result.get('similarity', 0) * 100
                            
                            # Show brief popup
                            self.show_copy_confirmation(f"Copied! [{similarity:.0f}%] {source}")
                            return
                except:
                    continue
                    
        except Exception as e:
            logger.error(f"Copy error: {e}")
            
    def show_copy_confirmation(self, message: str):
        """Show brief copy confirmation."""
        # Create temporary label
        confirm_label = ttk.Label(self.root, text=f"📋 {message}", 
                                 background='#2ecc71', foreground='white',
                                 font=('Segoe UI', 8, 'bold'), padding=5)
        confirm_label.place(x=10, y=10)
        
        # Remove after 2 seconds
        self.root.after(2000, confirm_label.destroy)
        
    def on_closing(self):
        """Handle closing."""
        if self.is_monitoring:
            self.monitor.stop_monitoring()
        self.root.destroy()
        
    def run(self):
        """Run the compact app."""
        self.root.mainloop()

def main():
    """Main entry point."""
    print("🔍 Compact Side Window Real-time Search Monitor")
    print("=" * 50)
    print("✨ Features:")
    print("  • Compact side window design")
    print("  • Always stays on top")
    print("  • Positioned on right side")
    print("  • Real-time global monitoring")
    print("  • Instant search results")
    print("=" * 50)
    print()
    
    if not MONITORING_AVAILABLE:
        print("⚠️  Install for global monitoring:")
        print("   pip install keyboard pyperclip")
        print("   (Run as administrator)")
        print()
    
    try:
        app = CompactSideWindow()
        app.run()
    except Exception as e:
        print(f"Error: {e}")
        messagebox.showerror("Error", f"Application error: {e}")

if __name__ == "__main__":
    main()
