# Semantic Search Assistant - Real-time Text Suggestions

A desktop application that provides **real-time text suggestions** as you type in any text editor. Features **letter-by-letter search** and **spacebar clearing** functionality.

## 🚀 Quick Start

**Lightweight version (starts instantly):**

```bash
python run_app.py
```

**OR use the batch file:**

```bash
start.bat
```

## ✨ Features

- **Real-time monitoring**: Captures every keystroke in any text editor
- **Letter-by-letter search**: Each character triggers instant search
- **Spacebar clearing**: Press spacebar to clear search for next word
- **Document indexing**: Automatically indexes files from `test_docs/` folder
- **High relevance**: Only shows suggestions with >50% similarity
- **Copy & paste**: Double-click suggestions to copy with citations

## 📋 Requirements

```bash
pip install -r requirements.txt
```

## 📁 File Structure

```
Semantic_Search_Assistant/
├── app.py                          # Main application entry point
├── enhanced_realtime_monitor.py    # Real-time text monitoring
├── api_service.py                  # FastAPI backend
├── start_backend.py               # Backend startup script
├── database.py                    # Vector database
├── document_processor.py          # Document processing
├── search_engine.py              # Search functionality
├── test_docs/                     # Place your documents here
├── config.json                    # Configuration
└── requirements.txt               # Dependencies
```

## 🎯 Usage

1. **Start the application**: `python app.py`
2. **Open any text editor** (Word, Notepad, VS Code, etc.)
3. **Start typing**: Watch suggestions appear for each letter
4. **Press spacebar**: Clears search and starts fresh for next word
5. **Double-click suggestions**: Copies them to clipboard

## 🔧 Configuration

Edit `config.json` to customize:

- Embedding model
- Search thresholds
- Chunk sizes
- Real-time settings

## 📚 Document Indexing

Place your documents in the `test_docs/` folder:

- Supported formats: TXT, PDF, DOCX, MD
- Files are automatically indexed on startup
- Add new files and restart to index them

---

**Ready to use!** Run `python app.py` to start your real-time text suggestion system.
