#!/usr/bin/env python3
"""
Enhanced Real-time Text Monitor.
Monitors typing letter-by-letter across all text editors and provides instant suggestions.
Clears search on spacebar press for next word.
"""

import time
import threading
import logging
import requests
import pyperclip
import win32gui
import win32con
import keyboard
import re
import json
from typing import Optional, List, Dict, Any, Callable
from collections import deque

logger = logging.getLogger(__name__)

class EnhancedRealtimeMonitor:
    """Enhanced real-time typing monitor with letter-by-letter search."""
    
    def __init__(self, suggestion_callback: Callable[[str, List[Dict]], None]):
        self.suggestion_callback = suggestion_callback
        self.is_monitoring = False
        self.monitor_thread = None
        
        # Typing state
        self.current_word = ""
        self.typing_buffer = ""
        self.last_activity_time = 0
        self.last_search_time = 0
        
        # Performance optimization
        self.search_debounce_ms = 100  # 100ms debounce
        self.min_search_length = 2     # Minimum 2 characters to search
        self.max_buffer_size = 500     # Keep buffer manageable
        
        # Backend configuration
        self.backend_url = "http://127.0.0.1:8000"
        self.session = None
        
        # Text editor detection - expanded list with more variations
        self.text_editors = [
            'notepad', 'wordpad', 'word', 'microsoft word', 'winword',
            'document', 'doc', 'docx', 'office', 'microsoft office',
            'visual studio code', 'vscode', 'code', 'sublime text', 'atom',
            'notion', 'obsidian', 'typora', 'bear', 'roam', 'logseq',
            'google docs', 'writer', 'textedit', 'textpad',
            'scrivener', 'draft', 'ulysses', 'markdown', 'md',
            'vim', 'emacs', 'nano', 'gedit', 'kate', 'notepad++',
            'brackets', 'webstorm', 'pycharm', 'intellij'
        ]
        
    def start_monitoring(self):
        """Start enhanced real-time monitoring."""
        if self.is_monitoring:
            return

        self.is_monitoring = True

        # Start keyboard monitoring in separate thread
        self.monitor_thread = threading.Thread(target=self._keyboard_monitor_loop, daemon=True)
        self.monitor_thread.start()

        logger.info("Enhanced real-time monitoring started")

    def stop_monitoring(self):
        """Stop monitoring."""
        self.is_monitoring = False

        if self.monitor_thread:
            self.monitor_thread.join(timeout=2)

        # Unhook keyboard
        try:
            keyboard.unhook_all()
        except:
            pass

        logger.info("Enhanced real-time monitoring stopped")
    
    def _keyboard_monitor_loop(self):
        """Main keyboard monitoring loop."""
        def on_key_event(event):
            try:
                if not self.is_monitoring:
                    return
                
                current_time = time.time()
                
                # Check if we're in a text application
                if not self._is_in_text_editor():
                    return
                
                # Reset if too much time passed (5 seconds)
                if current_time - self.last_activity_time > 5:
                    self.current_word = ""
                    self.typing_buffer = ""
                
                self.last_activity_time = current_time
                
                # Handle key events
                if event.event_type == keyboard.KEY_DOWN:
                    self._handle_key_press(event, current_time)
                    
            except Exception as e:
                logger.error(f"Error in keyboard hook: {e}")
        
        # Register keyboard hook
        keyboard.hook(on_key_event)
        
        # Keep thread alive
        while self.is_monitoring:
            time.sleep(0.1)
    
    def _handle_key_press(self, event, current_time):
        """Handle individual key press events."""
        key_name = event.name.lower()
        
        if key_name == 'space':
            # Spacebar pressed - clear current word and start new search
            self._handle_spacebar()
            
        elif key_name == 'backspace':
            # Handle backspace
            self._handle_backspace()
            
        elif key_name in ['enter', 'tab']:
            # Word boundary - clear current word
            self._clear_current_word()
            
        elif len(key_name) == 1 and key_name.isalnum():
            # Regular character - add to current word
            self._add_character_to_word(key_name, current_time)
            
        elif key_name in ['period', 'comma', 'semicolon', 'colon', 'exclamation', 'question']:
            # Punctuation - might end word
            self._handle_punctuation(key_name)
    
    def _handle_spacebar(self):
        """Handle spacebar press - clear search and start fresh."""
        if self.current_word:
            logger.debug(f"Spacebar pressed, clearing word: '{self.current_word}'")
        
        # Clear current word but keep in typing buffer
        if self.current_word:
            self.typing_buffer += self.current_word + " "
            
        self.current_word = ""
        
        # Clear suggestions by sending empty search
        self._trigger_search_clear()
    
    def _handle_backspace(self):
        """Handle backspace key."""
        if self.current_word:
            self.current_word = self.current_word[:-1]
            logger.debug(f"Backspace, current word: '{self.current_word}'")
            
            # Trigger search with updated word
            if len(self.current_word) >= self.min_search_length:
                self._trigger_search(self.current_word)
            else:
                self._trigger_search_clear()
    
    def _add_character_to_word(self, char, current_time):
        """Add character to current word and trigger search."""
        self.current_word += char
        logger.debug(f"Added '{char}', current word: '{self.current_word}'")
        
        # Trigger search if word is long enough and debounce time passed
        if (len(self.current_word) >= self.min_search_length and 
            current_time - self.last_search_time > self.search_debounce_ms / 1000):
            
            self._trigger_search(self.current_word)
            self.last_search_time = current_time
    
    def _handle_punctuation(self, punct):
        """Handle punctuation marks."""
        # Add punctuation to buffer but might end current word
        if self.current_word:
            self.typing_buffer += self.current_word + punct + " "
            self.current_word = ""
            self._trigger_search_clear()
    
    def _clear_current_word(self):
        """Clear current word."""
        if self.current_word:
            self.typing_buffer += self.current_word + " "
            self.current_word = ""
            self._trigger_search_clear()
    
    def _trigger_search(self, query):
        """Trigger search with current query."""
        if not query or len(query.strip()) < self.min_search_length:
            return
        
        # Run search in background thread to avoid blocking
        threading.Thread(
            target=self._perform_search,
            args=(query.strip(),),
            daemon=True
        ).start()
    
    def _trigger_search_clear(self):
        """Clear search results."""
        try:
            self.suggestion_callback("", [])
        except Exception as e:
            logger.error(f"Error clearing suggestions: {e}")
    
    def _perform_search(self, query):
        """Perform actual search request."""
        try:
            response = requests.post(
                f"{self.backend_url}/search/fast",  # Use fast search endpoint
                json={
                    "query": query,
                    "limit": 6,  # Fewer results for faster response
                    "similarity_threshold": 0.5  # Higher threshold for better relevance
                },
                timeout=1.0  # Very fast timeout for real-time feel
            )
            
            if response.status_code == 200:
                data = response.json()
                results = data.get("results", [])
                
                # Filter results with score > 50 as requested
                filtered_results = [
                    r for r in results 
                    if r.get('similarity', 0) * 100 > 50
                ]
                
                # Call suggestion callback
                self.suggestion_callback(query, filtered_results)
                
            else:
                logger.warning(f"Search request failed: {response.status_code}")
                
        except requests.exceptions.Timeout:
            logger.debug(f"Search timeout for query: {query}")
        except Exception as e:
            logger.error(f"Error performing search for '{query}': {e}")
    
    def _is_in_text_editor(self):
        """Check if current active window is a text editor."""
        try:
            hwnd = win32gui.GetForegroundWindow()
            window_title = win32gui.GetWindowText(hwnd).lower()

            if not window_title:
                return False

            # Debug: Log the current window title
            logger.debug(f"Active window: '{window_title}'")

            # Check if any text editor name is in the window title
            is_editor = any(editor in window_title for editor in self.text_editors)

            if is_editor:
                logger.debug(f"Text editor detected: {window_title}")

            return is_editor

        except Exception as e:
            logger.error(f"Error checking active window: {e}")
            return False
    
    def get_current_context(self):
        """Get current typing context."""
        full_context = self.typing_buffer + self.current_word
        return full_context.strip()
    
    def force_search(self, query):
        """Force a search with given query."""
        self._trigger_search(query)


class FloatingSuggestionUI:
    """Floating UI window for displaying real-time suggestions."""

    def __init__(self):
        import tkinter as tk
        from tkinter import ttk

        # Store references
        self.tk = tk
        self.ttk = ttk

        self.root = tk.Tk()
        self.setup_window()
        self.setup_ui()

        # State
        self.current_suggestions = []
        self.current_query = ""

    def setup_window(self):
        """Setup floating window properties."""
        self.root.title("Real-time Suggestions")
        self.root.geometry("400x300")

        # Make window float above others
        self.root.wm_attributes("-topmost", True)
        self.root.wm_attributes("-alpha", 0.95)

        # Position in top-right corner
        screen_width = self.root.winfo_screenwidth()
        self.root.geometry(f"400x300+{screen_width-420}+50")

        # Remove window decorations for cleaner look
        self.root.overrideredirect(False)

        # Configure colors
        self.root.configure(bg='#f8f9fa')

    def setup_ui(self):
        """Setup UI components."""
        tk = self.tk
        ttk = self.ttk

        # Header
        header_frame = tk.Frame(self.root, bg='#343a40', height=40)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        title_label = tk.Label(
            header_frame,
            text="🔍 Real-time Suggestions",
            bg='#343a40',
            fg='white',
            font=('Segoe UI', 10, 'bold')
        )
        title_label.pack(side=tk.LEFT, padx=10, pady=10)

        # Current query display
        self.query_label = tk.Label(
            self.root,
            text="Start typing in any text editor...",
            bg='#f8f9fa',
            fg='#6c757d',
            font=('Segoe UI', 9),
            wraplength=380
        )
        self.query_label.pack(pady=5, padx=10, fill=tk.X)

        # Suggestions area
        suggestions_frame = tk.Frame(self.root, bg='#f8f9fa')
        suggestions_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Scrollable text widget for suggestions
        self.suggestions_text = tk.Text(
            suggestions_frame,
            wrap=tk.WORD,
            bg='white',
            fg='#212529',
            font=('Segoe UI', 9),
            relief=tk.FLAT,
            bd=1,
            padx=10,
            pady=10
        )

        scrollbar = ttk.Scrollbar(suggestions_frame, orient=tk.VERTICAL, command=self.suggestions_text.yview)
        self.suggestions_text.configure(yscrollcommand=scrollbar.set)

        self.suggestions_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Bind double-click to copy
        self.suggestions_text.bind("<Double-Button-1>", self.on_suggestion_click)

        # Status bar
        status_frame = tk.Frame(self.root, bg='#e9ecef', height=25)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        status_frame.pack_propagate(False)

        self.status_label = tk.Label(
            status_frame,
            text="● Waiting for backend...",
            bg='#e9ecef',
            fg='#6c757d',
            font=('Segoe UI', 8)
        )
        self.status_label.pack(side=tk.LEFT, padx=10, pady=3)

    def update_suggestions(self, query: str, suggestions: List[Dict]):
        """Update the suggestions display."""
        self.current_query = query
        self.current_suggestions = suggestions

        # Update query display
        if query:
            self.query_label.config(text=f"Searching: '{query}'")
        else:
            self.query_label.config(text="Start typing in any text editor...")

        # Clear and update suggestions
        self.suggestions_text.config(state=self.tk.NORMAL)
        self.suggestions_text.delete(1.0, self.tk.END)

        if not suggestions:
            if query:
                self.suggestions_text.insert(self.tk.END, "No relevant suggestions found.\n\n")
                self.suggestions_text.insert(self.tk.END, "💡 Try typing more characters or different words.")
            else:
                self.suggestions_text.insert(self.tk.END, "🚀 Start typing in any text editor to see suggestions!\n\n")
                self.suggestions_text.insert(self.tk.END, "Supported editors:\n")
                self.suggestions_text.insert(self.tk.END, "• Notepad, WordPad, Microsoft Word\n")
                self.suggestions_text.insert(self.tk.END, "• Visual Studio Code, Sublime Text\n")
                self.suggestions_text.insert(self.tk.END, "• Notion, Obsidian, Typora\n")
                self.suggestions_text.insert(self.tk.END, "• And many more...")
        else:
            for i, suggestion in enumerate(suggestions, 1):
                content = suggestion.get("content", "")
                source = suggestion.get("source", "Unknown")
                similarity = suggestion.get("similarity", 0) * 100

                # Add suggestion header
                self.suggestions_text.insert(self.tk.END, f"📄 Result {i} ", "header")
                self.suggestions_text.insert(self.tk.END, f"({similarity:.0f}% match)\n", "similarity")
                self.suggestions_text.insert(self.tk.END, f"📁 {source}\n\n", "source")

                # Add content preview
                preview = content[:150] + "..." if len(content) > 150 else content
                self.suggestions_text.insert(self.tk.END, f"{preview}\n\n", "content")

                # Add separator
                self.suggestions_text.insert(self.tk.END, "─" * 50 + "\n\n", "separator")

        # Configure text tags for styling
        self.suggestions_text.tag_config("header", font=("Segoe UI", 9, "bold"), foreground="#495057")
        self.suggestions_text.tag_config("similarity", font=("Segoe UI", 8), foreground="#28a745")
        self.suggestions_text.tag_config("source", font=("Segoe UI", 8), foreground="#007bff")
        self.suggestions_text.tag_config("content", font=("Segoe UI", 9), foreground="#212529")
        self.suggestions_text.tag_config("separator", foreground="#dee2e6")

        self.suggestions_text.config(state=self.tk.DISABLED)

        # Update status
        if suggestions:
            self.status_label.config(text=f"● Found {len(suggestions)} suggestions", fg="#28a745")
        else:
            self.status_label.config(text="● No suggestions", fg="#6c757d")

    def on_suggestion_click(self, event):
        """Handle click on suggestion to copy it."""
        if self.current_suggestions:
            # For now, copy the first suggestion
            # Could be enhanced to detect which specific suggestion was clicked
            suggestion = self.current_suggestions[0]
            content = suggestion.get("content", "")
            source = suggestion.get("source", "Unknown")

            # Copy to clipboard with citation
            citation_text = f"{content}\n\n[Source: {source}]"
            pyperclip.copy(citation_text)

            # Show feedback
            self.status_label.config(text="✅ Copied to clipboard!", fg="#28a745")
            self.root.after(2000, lambda: self.status_label.config(text="● Ready", fg="#6c757d"))

    def show(self):
        """Show the floating window."""
        self.root.deiconify()
        self.root.lift()

    def hide(self):
        """Hide the floating window."""
        self.root.withdraw()

    def run(self):
        """Run the UI main loop."""
        self.root.mainloop()


def test_enhanced_monitor():
    """Test the enhanced real-time monitor."""
    import tkinter as tk

    # Create floating UI
    ui = FloatingSuggestionUI()

    def on_suggestions(query, suggestions):
        """Handle new suggestions."""
        print(f"Query: '{query}' -> {len(suggestions)} suggestions")
        ui.update_suggestions(query, suggestions)

    # Create monitor
    monitor = EnhancedRealtimeMonitor(on_suggestions)

    # Start monitoring
    monitor.start_monitoring()

    print("🚀 Enhanced Real-time Monitor Started!")
    print("📝 Start typing in any text editor to see suggestions")
    print("🔤 Each letter will trigger a new search")
    print("⎵  Press spacebar to clear search for next word")
    print("⏹️  Close the window to stop")

    # Run UI
    ui.run()


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    test_enhanced_monitor()
