2025-07-18 21:01:56,970 - __main__ - INFO - Starting Semantic Search Assistant backend...
2025-07-18 21:02:08,577 - __main__ - ERROR - Failed to start backend: No module named 'document_monitor'
2025-07-18 21:06:00,976 - __main__ - INFO - Starting Semantic Search Assistant backend...
2025-07-18 21:06:10,886 - __main__ - ERROR - Failed to start backend: No module named 'document_monitor'
2025-07-18 21:43:53,437 - __main__ - INFO - Starting Semantic Search Assistant backend...
2025-07-18 21:54:02,014 - __main__ - ERROR - Failed to start backend: No module named 'document_monitor'
2025-07-18 21:59:22,981 - __main__ - INFO - Starting Semantic Search Assistant backend...
2025-07-18 21:59:59,917 - __main__ - ERROR - Failed to start backend: No module named 'document_monitor'
2025-07-18 22:01:49,698 - __main__ - INFO - Starting Semantic Search Assistant backend...
2025-07-18 22:02:05,265 - folder_manager - INFO - Loaded 2 connected folders
2025-07-18 22:02:05,374 - main - INFO - Initializing document search backend...
2025-07-18 22:02:05,375 - database - INFO - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-18 22:02:05,377 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-18 22:02:05,380 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-07-18 22:02:14,436 - database - INFO - Created new table: documents
2025-07-18 22:02:14,437 - database - INFO - Vector store initialized successfully
2025-07-18 22:02:14,437 - document_processor - INFO - Document processor initialized
2025-07-18 22:02:14,722 - main - INFO - Added test_docs folder to monitoring: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-18 22:02:14,722 - folder_manager - INFO - Starting folder monitoring...
2025-07-18 22:02:14,729 - folder_manager - INFO - Started monitoring folder: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-18 22:02:14,730 - folder_manager - WARNING - Folder does not exist, skipping monitoring: C:\Users\<USER>\OneDrive\Desktop\BilalProject\Semantic_Search_Assistant\test_docs
2025-07-18 22:02:14,731 - folder_manager - INFO - Monitoring 2 folders
2025-07-18 22:02:14,737 - api_service - INFO - API server started successfully
2025-07-18 22:02:14,755 - folder_manager - INFO - Starting background document processor...
2025-07-18 22:03:33,979 - __main__ - INFO - Starting Semantic Search Assistant backend...
2025-07-18 22:05:13,345 - folder_manager - INFO - Loaded 2 connected folders
2025-07-18 22:05:13,346 - main - INFO - Initializing document search backend...
2025-07-18 22:05:13,346 - database - INFO - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-18 22:05:13,348 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-18 22:05:13,348 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-07-18 22:05:16,963 - __main__ - INFO - Starting Semantic Search Assistant backend...
2025-07-18 22:05:19,603 - database - INFO - Connected to existing table: documents
2025-07-18 22:05:19,605 - database - INFO - Vector store initialized successfully
2025-07-18 22:05:19,605 - document_processor - INFO - Document processor initialized
2025-07-18 22:05:19,641 - main - INFO - Added test_docs folder to monitoring: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-18 22:05:19,642 - folder_manager - INFO - Starting folder monitoring...
2025-07-18 22:05:19,642 - folder_manager - WARNING - Folder does not exist, skipping monitoring: C:\Users\<USER>\OneDrive\Desktop\BilalProject\Semantic_Search_Assistant\test_docs
2025-07-18 22:05:19,652 - folder_manager - INFO - Started monitoring folder: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-18 22:05:19,652 - folder_manager - INFO - Monitoring 2 folders
2025-07-18 22:05:19,689 - api_service - INFO - API server started successfully
2025-07-18 22:05:19,700 - folder_manager - INFO - Starting background document processor...
2025-07-18 22:05:27,031 - folder_manager - INFO - Loaded 2 connected folders
2025-07-18 22:05:27,032 - main - INFO - Initializing document search backend...
2025-07-18 22:05:27,033 - database - INFO - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-18 22:05:27,034 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-18 22:05:27,034 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-07-18 22:05:32,676 - database - INFO - No documents in vector store yet
2025-07-18 22:05:32,677 - main - INFO - Search for 'h' returned 0 results
2025-07-18 22:05:32,863 - database - INFO - Connected to existing table: documents
2025-07-18 22:05:32,863 - database - INFO - Vector store initialized successfully
2025-07-18 22:05:32,864 - document_processor - INFO - Document processor initialized
2025-07-18 22:05:32,883 - main - INFO - Added test_docs folder to monitoring: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-18 22:05:32,883 - folder_manager - INFO - Starting folder monitoring...
2025-07-18 22:05:32,884 - folder_manager - WARNING - Folder does not exist, skipping monitoring: C:\Users\<USER>\OneDrive\Desktop\BilalProject\Semantic_Search_Assistant\test_docs
2025-07-18 22:05:32,885 - folder_manager - INFO - Started monitoring folder: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-18 22:05:32,889 - folder_manager - INFO - Monitoring 2 folders
2025-07-18 22:05:32,893 - api_service - INFO - API server started successfully
2025-07-18 22:05:32,909 - folder_manager - INFO - Starting background document processor...
2025-07-18 22:05:32,929 - database - INFO - Vector store closed
2025-07-18 22:05:32,930 - api_service - INFO - API server shutdown complete
2025-07-18 22:05:33,010 - folder_manager - INFO - Background processor stopped
2025-07-18 22:05:33,047 - database - INFO - No documents in vector store yet
2025-07-18 22:05:33,048 - main - INFO - Search for 'hh' returned 0 results
2025-07-18 22:05:33,740 - database - INFO - No documents in vector store yet
2025-07-18 22:05:33,740 - main - INFO - Search for 'hhv' returned 0 results
2025-07-18 22:05:34,134 - database - INFO - No documents in vector store yet
2025-07-18 22:05:34,135 - main - INFO - Search for 'hhvv' returned 0 results
2025-07-18 22:05:37,373 - database - INFO - No documents in vector store yet
2025-07-18 22:05:37,374 - main - INFO - Search for 'hhvvs' returned 0 results
2025-07-18 22:05:37,969 - database - INFO - No documents in vector store yet
2025-07-18 22:05:37,970 - main - INFO - Search for 'hhvvss' returned 0 results
2025-07-18 22:05:38,277 - database - INFO - No documents in vector store yet
2025-07-18 22:05:38,277 - main - INFO - Search for 'hhvvssd' returned 0 results
2025-07-18 22:05:38,705 - database - INFO - No documents in vector store yet
2025-07-18 22:05:38,705 - main - INFO - Search for 'hhvvssdd' returned 0 results
2025-07-18 22:05:46,875 - database - INFO - No documents in vector store yet
2025-07-18 22:05:46,876 - main - INFO - Search for 'b' returned 0 results
2025-07-18 22:05:47,708 - database - INFO - No documents in vector store yet
2025-07-18 22:05:47,708 - main - INFO - Search for 'bi' returned 0 results
2025-07-18 22:05:47,989 - database - INFO - No documents in vector store yet
2025-07-18 22:05:47,989 - main - INFO - Search for 'bil' returned 0 results
2025-07-18 22:05:48,055 - database - INFO - No documents in vector store yet
2025-07-18 22:05:48,055 - main - INFO - Search for 'bila' returned 0 results
2025-07-18 22:05:48,150 - database - INFO - No documents in vector store yet
2025-07-18 22:05:48,150 - main - INFO - Search for 'bilal' returned 0 results
2025-07-18 22:05:56,997 - database - INFO - No documents in vector store yet
2025-07-18 22:05:56,998 - main - INFO - Search for 'd' returned 0 results
2025-07-18 22:05:57,005 - database - INFO - No documents in vector store yet
2025-07-18 22:05:57,005 - main - INFO - Search for 'df' returned 0 results
2025-07-18 22:05:57,041 - database - INFO - No documents in vector store yet
2025-07-18 22:05:57,041 - main - INFO - Search for 'dfs' returned 0 results
2025-07-18 22:05:57,490 - database - INFO - No documents in vector store yet
2025-07-18 22:05:57,491 - main - INFO - Search for 'dfsf' returned 0 results
2025-07-18 22:05:57,508 - database - INFO - No documents in vector store yet
2025-07-18 22:05:57,509 - main - INFO - Search for 'dfsfs' returned 0 results
2025-07-18 22:05:58,043 - database - INFO - No documents in vector store yet
2025-07-18 22:05:58,044 - main - INFO - Search for 'dfsf' returned 0 results
2025-07-18 22:05:58,203 - database - INFO - No documents in vector store yet
2025-07-18 22:05:58,204 - main - INFO - Search for 'dfs' returned 0 results
2025-07-18 22:05:58,546 - database - INFO - No documents in vector store yet
2025-07-18 22:05:58,547 - main - INFO - Search for 'df' returned 0 results
2025-07-18 22:05:58,668 - database - INFO - No documents in vector store yet
2025-07-18 22:05:58,669 - main - INFO - Search for 'd' returned 0 results
2025-07-18 22:05:59,205 - database - INFO - No documents in vector store yet
2025-07-18 22:05:59,206 - main - INFO - Search for 'd' returned 0 results
2025-07-18 22:05:59,322 - database - INFO - No documents in vector store yet
2025-07-18 22:05:59,323 - main - INFO - Search for 'df' returned 0 results
2025-07-18 22:05:59,515 - database - INFO - No documents in vector store yet
2025-07-18 22:05:59,516 - main - INFO - Search for 'dff' returned 0 results
2025-07-18 22:05:59,635 - database - INFO - No documents in vector store yet
2025-07-18 22:05:59,636 - main - INFO - Search for 'dffd' returned 0 results
2025-07-18 22:05:59,772 - database - INFO - No documents in vector store yet
2025-07-18 22:05:59,772 - main - INFO - Search for 'dffdf' returned 0 results
2025-07-18 22:05:59,917 - database - INFO - No documents in vector store yet
2025-07-18 22:05:59,917 - main - INFO - Search for 'dffdfd' returned 0 results
2025-07-18 22:06:00,007 - database - INFO - No documents in vector store yet
2025-07-18 22:06:00,010 - main - INFO - Search for 'dffdfdf' returned 0 results
2025-07-18 22:06:00,288 - database - INFO - No documents in vector store yet
2025-07-18 22:06:00,289 - main - INFO - Search for 'dffdfd' returned 0 results
2025-07-18 22:06:00,423 - database - INFO - No documents in vector store yet
2025-07-18 22:06:00,424 - main - INFO - Search for 'dffdf' returned 0 results
2025-07-18 22:06:00,554 - database - INFO - No documents in vector store yet
2025-07-18 22:06:00,555 - main - INFO - Search for 'dffd' returned 0 results
2025-07-18 22:06:00,672 - database - INFO - No documents in vector store yet
2025-07-18 22:06:00,672 - main - INFO - Search for 'dff' returned 0 results
2025-07-18 22:06:00,804 - database - INFO - No documents in vector store yet
2025-07-18 22:06:00,804 - main - INFO - Search for 'df' returned 0 results
2025-07-18 22:06:00,939 - database - INFO - No documents in vector store yet
2025-07-18 22:06:00,940 - main - INFO - Search for 'd' returned 0 results
2025-07-18 22:06:01,509 - database - INFO - No documents in vector store yet
2025-07-18 22:06:01,509 - main - INFO - Search for 'f' returned 0 results
2025-07-18 22:06:01,659 - database - INFO - No documents in vector store yet
2025-07-18 22:06:01,660 - main - INFO - Search for 'fd' returned 0 results
2025-07-18 22:06:01,810 - database - INFO - No documents in vector store yet
2025-07-18 22:06:01,810 - main - INFO - Search for 'fdf' returned 0 results
2025-07-18 22:06:47,022 - database - INFO - No documents in vector store yet
2025-07-18 22:06:47,023 - main - INFO - Search for 'fdfd' returned 0 results
2025-07-18 22:06:47,255 - database - INFO - No documents in vector store yet
2025-07-18 22:06:47,255 - main - INFO - Search for 'fdfdf' returned 0 results
2025-07-18 22:06:47,446 - database - INFO - No documents in vector store yet
2025-07-18 22:06:47,446 - main - INFO - Search for 'fdfdff' returned 0 results
2025-07-18 22:06:47,655 - database - INFO - No documents in vector store yet
2025-07-18 22:06:47,656 - main - INFO - Search for 'fdfdffd' returned 0 results
2025-07-18 22:06:47,938 - database - INFO - No documents in vector store yet
2025-07-18 22:06:47,938 - main - INFO - Search for 'fdfdffdf' returned 0 results
2025-07-18 22:06:48,083 - database - INFO - No documents in vector store yet
2025-07-18 22:06:48,083 - main - INFO - Search for 'fdfdffdfd' returned 0 results
2025-07-18 22:06:48,818 - database - INFO - No documents in vector store yet
2025-07-18 22:06:48,819 - main - INFO - Search for 'fdfdffdf' returned 0 results
2025-07-18 22:06:48,991 - database - INFO - No documents in vector store yet
2025-07-18 22:06:48,992 - main - INFO - Search for 'fdfdffd' returned 0 results
2025-07-18 22:06:49,091 - database - INFO - No documents in vector store yet
2025-07-18 22:06:49,091 - main - INFO - Search for 'fdfdff' returned 0 results
2025-07-18 22:06:49,214 - database - INFO - No documents in vector store yet
2025-07-18 22:06:49,214 - main - INFO - Search for 'fdfdf' returned 0 results
2025-07-18 22:06:49,347 - database - INFO - No documents in vector store yet
2025-07-18 22:06:49,347 - main - INFO - Search for 'fdfd' returned 0 results
2025-07-18 22:06:49,468 - database - INFO - No documents in vector store yet
2025-07-18 22:06:49,469 - main - INFO - Search for 'fdf' returned 0 results
2025-07-18 22:06:49,598 - database - INFO - No documents in vector store yet
2025-07-18 22:06:49,598 - main - INFO - Search for 'fd' returned 0 results
2025-07-18 22:06:57,709 - database - INFO - No documents in vector store yet
2025-07-18 22:06:57,709 - main - INFO - Search for 'm' returned 0 results
2025-07-18 22:06:57,867 - database - INFO - No documents in vector store yet
2025-07-18 22:06:57,868 - main - INFO - Search for 'me' returned 0 results
2025-07-18 22:06:58,166 - database - INFO - No documents in vector store yet
2025-07-18 22:06:58,167 - main - INFO - Search for 'mee' returned 0 results
2025-07-18 22:06:58,390 - database - INFO - No documents in vector store yet
2025-07-18 22:06:58,390 - main - INFO - Search for 'meet' returned 0 results
2025-07-18 22:06:58,690 - database - INFO - No documents in vector store yet
2025-07-18 22:06:58,690 - main - INFO - Search for 'm' returned 0 results
2025-07-18 22:06:59,249 - database - INFO - No documents in vector store yet
2025-07-18 22:06:59,249 - main - INFO - Search for 'p' returned 0 results
2025-07-18 22:07:00,038 - database - INFO - No documents in vector store yet
2025-07-18 22:07:00,038 - main - INFO - Search for 'pe' returned 0 results
2025-07-18 22:07:00,362 - database - INFO - No documents in vector store yet
2025-07-18 22:07:00,363 - main - INFO - Search for 'a' returned 0 results
2025-07-18 22:07:00,507 - database - INFO - No documents in vector store yet
2025-07-18 22:07:00,508 - main - INFO - Search for 'aa' returned 0 results
2025-07-18 22:07:00,609 - database - INFO - No documents in vector store yet
2025-07-18 22:07:00,609 - main - INFO - Search for 'aaa' returned 0 results
2025-07-18 22:07:00,910 - database - INFO - No documents in vector store yet
2025-07-18 22:07:00,910 - main - INFO - Search for 'a' returned 0 results
2025-07-18 22:07:01,007 - database - INFO - No documents in vector store yet
2025-07-18 22:07:01,007 - main - INFO - Search for 'ai' returned 0 results
2025-07-18 22:07:01,166 - database - INFO - No documents in vector store yet
2025-07-18 22:07:01,166 - main - INFO - Search for 'aik' returned 0 results
2025-07-18 22:07:01,463 - database - INFO - No documents in vector store yet
2025-07-18 22:07:01,464 - main - INFO - Search for 'm' returned 0 results
2025-07-18 22:07:01,551 - database - INFO - No documents in vector store yet
2025-07-18 22:07:01,551 - main - INFO - Search for 'mi' returned 0 results
2025-07-18 22:07:01,696 - database - INFO - No documents in vector store yet
2025-07-18 22:07:01,696 - main - INFO - Search for 'min' returned 0 results
2025-07-18 22:07:02,691 - database - INFO - No documents in vector store yet
2025-07-18 22:07:02,692 - main - INFO - Search for 'a' returned 0 results
2025-07-18 22:07:02,912 - database - INFO - No documents in vector store yet
2025-07-18 22:07:02,912 - main - INFO - Search for 'ag' returned 0 results
2025-07-18 22:07:03,085 - database - INFO - No documents in vector store yet
2025-07-18 22:07:03,086 - main - INFO - Search for 'agr' returned 0 results
2025-07-18 22:07:03,322 - database - INFO - No documents in vector store yet
2025-07-18 22:07:03,322 - main - INFO - Search for 'f' returned 0 results
2025-07-18 22:07:03,518 - database - INFO - No documents in vector store yet
2025-07-18 22:07:03,519 - main - INFO - Search for 'fr' returned 0 results
2025-07-18 22:07:03,706 - database - INFO - No documents in vector store yet
2025-07-18 22:07:03,707 - main - INFO - Search for 'fre' returned 0 results
2025-07-18 22:07:03,827 - database - INFO - No documents in vector store yet
2025-07-18 22:07:03,827 - main - INFO - Search for 'free' returned 0 results
2025-07-18 22:07:04,384 - database - INFO - No documents in vector store yet
2025-07-18 22:07:04,385 - main - INFO - Search for 't' returned 0 results
2025-07-18 22:07:04,690 - database - INFO - No documents in vector store yet
2025-07-18 22:07:04,690 - main - INFO - Search for 'to' returned 0 results
2025-07-18 22:07:07,474 - database - INFO - No documents in vector store yet
2025-07-18 22:07:07,475 - main - INFO - Search for 'toa' returned 0 results
2025-07-18 22:07:07,576 - database - INFO - No documents in vector store yet
2025-07-18 22:07:07,576 - main - INFO - Search for 'toai' returned 0 results
2025-07-18 22:07:07,749 - database - INFO - No documents in vector store yet
2025-07-18 22:07:07,749 - main - INFO - Search for 'toaik' returned 0 results
2025-07-18 22:07:08,136 - database - INFO - No documents in vector store yet
2025-07-18 22:07:08,136 - main - INFO - Search for 'h' returned 0 results
2025-07-18 22:07:08,260 - database - INFO - No documents in vector store yet
2025-07-18 22:07:08,260 - main - INFO - Search for 'he' returned 0 results
2025-07-18 22:07:08,356 - database - INFO - No documents in vector store yet
2025-07-18 22:07:08,356 - main - INFO - Search for 'hea' returned 0 results
2025-07-18 22:07:08,500 - database - INFO - No documents in vector store yet
2025-07-18 22:07:08,500 - main - INFO - Search for 'heav' returned 0 results
2025-07-18 22:07:08,612 - database - INFO - No documents in vector store yet
2025-07-18 22:07:08,613 - main - INFO - Search for 'heavy' returned 0 results
2025-07-18 22:07:08,994 - database - INFO - No documents in vector store yet
2025-07-18 22:07:08,994 - main - INFO - Search for 'k' returned 0 results
2025-07-18 22:07:09,096 - database - INFO - No documents in vector store yet
2025-07-18 22:07:09,096 - main - INFO - Search for 'ka' returned 0 results
2025-07-18 22:07:09,434 - database - INFO - No documents in vector store yet
2025-07-18 22:07:09,435 - main - INFO - Search for 'kam' returned 0 results
2025-07-18 22:07:10,115 - database - INFO - No documents in vector store yet
2025-07-18 22:07:10,117 - main - INFO - Search for 'h' returned 0 results
2025-07-18 22:07:10,275 - database - INFO - No documents in vector store yet
2025-07-18 22:07:10,275 - main - INFO - Search for 'ho' returned 0 results
2025-07-18 22:07:10,779 - database - INFO - No documents in vector store yet
2025-07-18 22:07:10,779 - main - INFO - Search for 'g' returned 0 results
2025-07-18 22:07:10,847 - database - INFO - No documents in vector store yet
2025-07-18 22:07:10,847 - main - INFO - Search for 'gy' returned 0 results
2025-07-18 22:07:10,960 - database - INFO - No documents in vector store yet
2025-07-18 22:07:10,960 - main - INFO - Search for 'gya' returned 0 results
2025-07-18 22:07:11,646 - database - INFO - No documents in vector store yet
2025-07-18 22:07:11,646 - main - INFO - Search for 'h' returned 0 results
2025-07-18 22:07:11,762 - database - INFO - No documents in vector store yet
2025-07-18 22:07:11,762 - main - INFO - Search for 'ha' returned 0 results
2025-07-18 22:07:14,368 - database - INFO - No documents in vector store yet
2025-07-18 22:07:14,369 - main - INFO - Search for 'a' returned 0 results
2025-07-18 22:07:15,095 - database - INFO - No documents in vector store yet
2025-07-18 22:07:15,095 - main - INFO - Search for 'ab' returned 0 results
2025-07-18 22:07:15,398 - database - INFO - No documents in vector store yet
2025-07-18 22:07:15,399 - main - INFO - Search for 'w' returned 0 results
2025-07-18 22:07:15,572 - database - INFO - No documents in vector store yet
2025-07-18 22:07:15,572 - main - INFO - Search for 'wa' returned 0 results
2025-07-18 22:07:15,832 - database - INFO - No documents in vector store yet
2025-07-18 22:07:15,833 - main - INFO - Search for 'wap' returned 0 results
2025-07-18 22:07:16,026 - database - INFO - No documents in vector store yet
2025-07-18 22:07:16,026 - main - INFO - Search for 'wapi' returned 0 results
2025-07-18 22:07:16,143 - database - INFO - No documents in vector store yet
2025-07-18 22:07:16,144 - main - INFO - Search for 'wapis' returned 0 results
2025-07-18 22:07:16,513 - database - INFO - No documents in vector store yet
2025-07-18 22:07:16,514 - main - INFO - Search for 'a' returned 0 results
2025-07-18 22:07:16,662 - database - INFO - No documents in vector store yet
2025-07-18 22:07:16,663 - main - INFO - Search for 'aa' returned 0 results
2025-07-18 22:07:17,636 - database - INFO - No documents in vector store yet
2025-07-18 22:07:17,636 - main - INFO - Search for 'd' returned 0 results
2025-07-18 22:07:17,764 - database - INFO - No documents in vector store yet
2025-07-18 22:07:17,764 - main - INFO - Search for 'dh' returned 0 results
2025-07-18 22:07:17,919 - database - INFO - No documents in vector store yet
2025-07-18 22:07:17,928 - main - INFO - Search for 'dha' returned 0 results
2025-07-18 22:07:18,367 - database - INFO - No documents in vector store yet
2025-07-18 22:07:18,369 - main - INFO - Search for 'p' returned 0 results
2025-07-18 22:07:18,584 - database - INFO - No documents in vector store yet
2025-07-18 22:07:18,585 - main - INFO - Search for 'pu' returned 0 results
2025-07-18 22:07:18,710 - database - INFO - No documents in vector store yet
2025-07-18 22:07:18,710 - main - INFO - Search for 'pur' returned 0 results
2025-07-18 22:07:18,827 - database - INFO - No documents in vector store yet
2025-07-18 22:07:18,827 - main - INFO - Search for 'pura' returned 0 results
2025-07-18 22:07:18,964 - database - INFO - No documents in vector store yet
2025-07-18 22:07:18,964 - main - INFO - Search for 'puran' returned 0 results
2025-07-18 22:07:19,061 - database - INFO - No documents in vector store yet
2025-07-18 22:07:19,063 - main - INFO - Search for 'purana' returned 0 results
2025-07-18 22:07:19,335 - database - INFO - No documents in vector store yet
2025-07-18 22:07:19,335 - main - INFO - Search for 'w' returned 0 results
2025-07-18 22:07:19,462 - database - INFO - No documents in vector store yet
2025-07-18 22:07:19,463 - main - INFO - Search for 'wa' returned 0 results
2025-07-18 22:07:19,561 - database - INFO - No documents in vector store yet
2025-07-18 22:07:19,561 - main - INFO - Search for 'wal' returned 0 results
2025-07-18 22:07:19,703 - database - INFO - No documents in vector store yet
2025-07-18 22:07:19,704 - main - INFO - Search for 'wale' returned 0 results
2025-07-18 22:07:20,150 - database - INFO - No documents in vector store yet
2025-07-18 22:07:20,151 - main - INFO - Search for 'walea' returned 0 results
2025-07-18 22:07:20,471 - database - INFO - No documents in vector store yet
2025-07-18 22:07:20,472 - main - INFO - Search for 'wale' returned 0 results
2025-07-18 22:07:20,588 - database - INFO - No documents in vector store yet
2025-07-18 22:07:20,588 - main - INFO - Search for 'wal' returned 0 results
2025-07-18 22:07:20,747 - database - INFO - No documents in vector store yet
2025-07-18 22:07:20,747 - main - INFO - Search for 'wala' returned 0 results
2025-07-18 22:07:21,275 - database - INFO - No documents in vector store yet
2025-07-18 22:07:21,275 - main - INFO - Search for 'c' returned 0 results
2025-07-18 22:07:21,407 - database - INFO - No documents in vector store yet
2025-07-18 22:07:21,407 - main - INFO - Search for 'ch' returned 0 results
2025-07-18 22:07:21,469 - database - INFO - No documents in vector store yet
2025-07-18 22:07:21,470 - main - INFO - Search for 'cha' returned 0 results
2025-07-18 22:07:21,588 - database - INFO - No documents in vector store yet
2025-07-18 22:07:21,589 - main - INFO - Search for 'chay' returned 0 results
2025-07-18 22:07:21,674 - database - INFO - No documents in vector store yet
2025-07-18 22:07:21,674 - main - INFO - Search for 'chaye' returned 0 results
2025-07-18 22:09:33,123 - __main__ - INFO - Starting Semantic Search Assistant backend...
2025-07-18 22:09:44,466 - folder_manager - INFO - Loaded 2 connected folders
2025-07-18 22:09:44,467 - main - INFO - Initializing document search backend...
2025-07-18 22:09:44,467 - database - INFO - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-18 22:09:44,469 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-18 22:09:44,469 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-07-18 22:09:51,442 - database - INFO - Connected to existing table: documents
2025-07-18 22:09:51,442 - database - INFO - Vector store initialized successfully
2025-07-18 22:09:51,442 - document_processor - INFO - Document processor initialized
2025-07-18 22:09:51,473 - main - INFO - Added test_docs folder to monitoring: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-18 22:09:51,474 - folder_manager - INFO - Starting folder monitoring...
2025-07-18 22:09:51,475 - folder_manager - INFO - Started monitoring folder: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-18 22:09:51,476 - folder_manager - WARNING - Folder does not exist, skipping monitoring: C:\Users\<USER>\OneDrive\Desktop\BilalProject\Semantic_Search_Assistant\test_docs
2025-07-18 22:09:51,476 - folder_manager - INFO - Monitoring 2 folders
2025-07-18 22:09:51,480 - api_service - INFO - API server started successfully
2025-07-18 22:09:51,495 - folder_manager - INFO - Starting background document processor...
2025-07-18 22:09:52,603 - main - INFO - Processing document 1/6: test_docs\bilal.txt
2025-07-18 22:09:52,615 - document_processor - INFO - Processed bilal.txt: 1 chunks created
2025-07-18 22:09:52,615 - folder_manager - INFO - File modified detected: E:\PROJECT\Semantic_Search_Assistant\test_docs\bilal.txt
2025-07-18 22:09:52,615 - main - INFO - Processed test_docs\bilal.txt: 1 chunks, type: <class 'list'>
2025-07-18 22:09:52,617 - folder_manager - INFO - Queued file for processing: E:\PROJECT\Semantic_Search_Assistant\test_docs\bilal.txt (trigger: modified)
2025-07-18 22:09:52,617 - main - INFO - First chunk type: <class 'document_processor.DocumentChunk'>, has content: True
2025-07-18 22:09:52,618 - database - INFO - add_document called with 1 chunks, type: <class 'list'>
2025-07-18 22:09:52,618 - database - INFO - First chunk type: <class 'document_processor.DocumentChunk'>
2025-07-18 22:09:52,836 - folder_manager - INFO - Processing file: E:\PROJECT\Semantic_Search_Assistant\test_docs\bilal.txt
2025-07-18 22:09:52,843 - main - INFO - Processing document 1/1: E:\PROJECT\Semantic_Search_Assistant\test_docs\bilal.txt
2025-07-18 22:09:52,845 - document_processor - INFO - Processed bilal.txt: 1 chunks created
2025-07-18 22:09:52,850 - main - INFO - Processed E:\PROJECT\Semantic_Search_Assistant\test_docs\bilal.txt: 1 chunks, type: <class 'list'>
2025-07-18 22:09:52,853 - main - INFO - First chunk type: <class 'document_processor.DocumentChunk'>, has content: True
2025-07-18 22:09:52,854 - database - INFO - add_document called with 1 chunks, type: <class 'list'>
2025-07-18 22:09:52,857 - database - INFO - First chunk type: <class 'document_processor.DocumentChunk'>
2025-07-18 22:09:53,026 - database - INFO - Processing chunk 0: metadata type = <class 'dict'>, metadata = {'source': 'test_docs\\bilal.txt', 'filename': 'bilal.txt', 'extension': '.txt', 'file_size': 5, 'modified_time': 1752519129.0826256, 'format': 'text', 'lines': 1, 'words': 1, 'chunk_index': 0, 'total_chunks': 1, 'chunk_size': 5}
2025-07-18 22:09:53,060 - database - INFO - Added 1 chunks for document: bilal.txt
2025-07-18 22:09:53,069 - main - INFO - Processing document 2/6: test_docs\hello.txt
2025-07-18 22:09:53,074 - document_processor - INFO - Processed hello.txt: 1 chunks created
2025-07-18 22:09:53,075 - folder_manager - INFO - File modified detected: E:\PROJECT\Semantic_Search_Assistant\test_docs\hello.txt
2025-07-18 22:09:53,076 - main - INFO - Processed test_docs\hello.txt: 1 chunks, type: <class 'list'>
2025-07-18 22:09:53,077 - folder_manager - INFO - Queued file for processing: E:\PROJECT\Semantic_Search_Assistant\test_docs\hello.txt (trigger: modified)
2025-07-18 22:09:53,078 - main - INFO - First chunk type: <class 'document_processor.DocumentChunk'>, has content: True
2025-07-18 22:09:53,089 - database - INFO - add_document called with 1 chunks, type: <class 'list'>
2025-07-18 22:09:53,090 - database - INFO - First chunk type: <class 'document_processor.DocumentChunk'>
2025-07-18 22:09:53,091 - database - INFO - Processing chunk 0: metadata type = <class 'dict'>, metadata = {'source': 'E:\\PROJECT\\Semantic_Search_Assistant\\test_docs\\bilal.txt', 'filename': 'bilal.txt', 'extension': '.txt', 'file_size': 5, 'modified_time': 1752519129.0826256, 'format': 'text', 'lines': 1, 'words': 1, 'chunk_index': 0, 'total_chunks': 1, 'chunk_size': 5}
2025-07-18 22:09:53,131 - database - INFO - Added 1 chunks for document: bilal.txt
2025-07-18 22:09:53,134 - folder_manager - INFO - Successfully processed: E:\PROJECT\Semantic_Search_Assistant\test_docs\bilal.txt (0 chunks)
2025-07-18 22:09:53,212 - database - INFO - Processing chunk 0: metadata type = <class 'dict'>, metadata = {'source': 'test_docs\\hello.txt', 'filename': 'hello.txt', 'extension': '.txt', 'file_size': 5, 'modified_time': 1751918168.9787822, 'format': 'text', 'lines': 1, 'words': 1, 'chunk_index': 0, 'total_chunks': 1, 'chunk_size': 5}
2025-07-18 22:09:53,236 - database - INFO - Added 1 chunks for document: hello.txt
2025-07-18 22:09:53,239 - main - INFO - Processing document 3/6: test_docs\hi.txt
2025-07-18 22:09:53,253 - document_processor - INFO - Processed hi.txt: 1 chunks created
2025-07-18 22:09:53,254 - folder_manager - INFO - File modified detected: E:\PROJECT\Semantic_Search_Assistant\test_docs\hi.txt
2025-07-18 22:09:53,254 - main - INFO - Processed test_docs\hi.txt: 1 chunks, type: <class 'list'>
2025-07-18 22:09:53,255 - folder_manager - INFO - Queued file for processing: E:\PROJECT\Semantic_Search_Assistant\test_docs\hi.txt (trigger: modified)
2025-07-18 22:09:53,255 - main - INFO - First chunk type: <class 'document_processor.DocumentChunk'>, has content: True
2025-07-18 22:09:53,255 - database - INFO - add_document called with 1 chunks, type: <class 'list'>
2025-07-18 22:09:53,256 - database - INFO - First chunk type: <class 'document_processor.DocumentChunk'>
2025-07-18 22:09:53,488 - database - INFO - Processing chunk 0: metadata type = <class 'dict'>, metadata = {'source': 'test_docs\\hi.txt', 'filename': 'hi.txt', 'extension': '.txt', 'file_size': 28, 'modified_time': 1751918300.4052818, 'format': 'text', 'lines': 1, 'words': 5, 'chunk_index': 0, 'total_chunks': 1, 'chunk_size': 28}
2025-07-18 22:09:53,498 - database - INFO - Added 1 chunks for document: hi.txt
2025-07-18 22:09:53,503 - main - INFO - Processing document 4/6: test_docs\realtime_test.txt
2025-07-18 22:09:53,518 - document_processor - INFO - Processed realtime_test.txt: 2 chunks created
2025-07-18 22:09:53,519 - folder_manager - INFO - File modified detected: E:\PROJECT\Semantic_Search_Assistant\test_docs\realtime_test.txt
2025-07-18 22:09:53,520 - main - INFO - Processed test_docs\realtime_test.txt: 2 chunks, type: <class 'list'>
2025-07-18 22:09:53,520 - folder_manager - INFO - Queued file for processing: E:\PROJECT\Semantic_Search_Assistant\test_docs\realtime_test.txt (trigger: modified)
2025-07-18 22:09:53,521 - main - INFO - First chunk type: <class 'document_processor.DocumentChunk'>, has content: True
2025-07-18 22:09:53,522 - database - INFO - add_document called with 2 chunks, type: <class 'list'>
2025-07-18 22:09:53,522 - database - INFO - First chunk type: <class 'document_processor.DocumentChunk'>
2025-07-18 22:09:53,624 - database - INFO - Processing chunk 0: metadata type = <class 'dict'>, metadata = {'source': 'test_docs\\realtime_test.txt', 'filename': 'realtime_test.txt', 'extension': '.txt', 'file_size': 868, 'modified_time': 1752853555.3566787, 'format': 'text', 'lines': 27, 'words': 119, 'chunk_index': 0, 'total_chunks': 2, 'chunk_size': 471}
2025-07-18 22:09:53,624 - database - INFO - Processing chunk 1: metadata type = <class 'dict'>, metadata = {'source': 'test_docs\\realtime_test.txt', 'filename': 'realtime_test.txt', 'extension': '.txt', 'file_size': 868, 'modified_time': 1752853555.3566787, 'format': 'text', 'lines': 27, 'words': 119, 'chunk_index': 1, 'total_chunks': 2, 'chunk_size': 367}
2025-07-18 22:09:53,633 - database - INFO - Added 2 chunks for document: realtime_test.txt
2025-07-18 22:09:53,634 - main - INFO - Processing document 5/6: test_docs\test_indexing.txt
2025-07-18 22:09:53,646 - document_processor - INFO - Processed test_indexing.txt: 1 chunks created
2025-07-18 22:09:53,647 - folder_manager - INFO - File modified detected: E:\PROJECT\Semantic_Search_Assistant\test_docs\test_indexing.txt
2025-07-18 22:09:53,648 - main - INFO - Processed test_docs\test_indexing.txt: 1 chunks, type: <class 'list'>
2025-07-18 22:09:53,648 - folder_manager - INFO - Queued file for processing: E:\PROJECT\Semantic_Search_Assistant\test_docs\test_indexing.txt (trigger: modified)
2025-07-18 22:09:53,649 - main - INFO - First chunk type: <class 'document_processor.DocumentChunk'>, has content: True
2025-07-18 22:09:53,649 - database - INFO - add_document called with 1 chunks, type: <class 'list'>
2025-07-18 22:09:53,649 - database - INFO - First chunk type: <class 'document_processor.DocumentChunk'>
2025-07-18 22:09:53,698 - database - INFO - Processing chunk 0: metadata type = <class 'dict'>, metadata = {'source': 'test_docs\\test_indexing.txt', 'filename': 'test_indexing.txt', 'extension': '.txt', 'file_size': 420, 'modified_time': 1752519686.0287697, 'format': 'text', 'lines': 12, 'words': 65, 'chunk_index': 0, 'total_chunks': 1, 'chunk_size': 419}
2025-07-18 22:09:53,704 - database - INFO - Added 1 chunks for document: test_indexing.txt
2025-07-18 22:09:53,705 - main - INFO - Processing document 6/6: test_docs\wasay.txt
2025-07-18 22:09:53,715 - document_processor - INFO - Processed wasay.txt: 1 chunks created
2025-07-18 22:09:53,716 - folder_manager - INFO - File modified detected: E:\PROJECT\Semantic_Search_Assistant\test_docs\wasay.txt
2025-07-18 22:09:53,716 - main - INFO - Processed test_docs\wasay.txt: 1 chunks, type: <class 'list'>
2025-07-18 22:09:53,716 - folder_manager - INFO - Queued file for processing: E:\PROJECT\Semantic_Search_Assistant\test_docs\wasay.txt (trigger: modified)
2025-07-18 22:09:53,717 - main - INFO - First chunk type: <class 'document_processor.DocumentChunk'>, has content: True
2025-07-18 22:09:53,717 - database - INFO - add_document called with 1 chunks, type: <class 'list'>
2025-07-18 22:09:53,717 - database - INFO - First chunk type: <class 'document_processor.DocumentChunk'>
2025-07-18 22:09:53,737 - database - INFO - Processing chunk 0: metadata type = <class 'dict'>, metadata = {'source': 'test_docs\\wasay.txt', 'filename': 'wasay.txt', 'extension': '.txt', 'file_size': 15, 'modified_time': 1752519906.9360347, 'format': 'text', 'lines': 1, 'words': 4, 'chunk_index': 0, 'total_chunks': 1, 'chunk_size': 15}
2025-07-18 22:09:53,747 - database - INFO - Added 1 chunks for document: wasay.txt
2025-07-18 22:09:54,377 - folder_manager - INFO - Processing file: E:\PROJECT\Semantic_Search_Assistant\test_docs\hello.txt
2025-07-18 22:09:54,380 - main - INFO - Processing document 1/1: E:\PROJECT\Semantic_Search_Assistant\test_docs\hello.txt
2025-07-18 22:09:54,382 - document_processor - INFO - Processed hello.txt: 1 chunks created
2025-07-18 22:09:54,383 - main - INFO - Processed E:\PROJECT\Semantic_Search_Assistant\test_docs\hello.txt: 1 chunks, type: <class 'list'>
2025-07-18 22:09:54,384 - main - INFO - First chunk type: <class 'document_processor.DocumentChunk'>, has content: True
2025-07-18 22:09:54,384 - database - INFO - add_document called with 1 chunks, type: <class 'list'>
2025-07-18 22:09:54,385 - database - INFO - First chunk type: <class 'document_processor.DocumentChunk'>
2025-07-18 22:09:54,420 - database - INFO - Processing chunk 0: metadata type = <class 'dict'>, metadata = {'source': 'E:\\PROJECT\\Semantic_Search_Assistant\\test_docs\\hello.txt', 'filename': 'hello.txt', 'extension': '.txt', 'file_size': 5, 'modified_time': 1751918168.9787822, 'format': 'text', 'lines': 1, 'words': 1, 'chunk_index': 0, 'total_chunks': 1, 'chunk_size': 5}
2025-07-18 22:09:54,444 - database - INFO - Added 1 chunks for document: hello.txt
2025-07-18 22:09:54,448 - folder_manager - INFO - Successfully processed: E:\PROJECT\Semantic_Search_Assistant\test_docs\hello.txt (0 chunks)
2025-07-18 22:09:54,449 - folder_manager - INFO - Processing file: E:\PROJECT\Semantic_Search_Assistant\test_docs\hi.txt
2025-07-18 22:09:54,450 - main - INFO - Processing document 1/1: E:\PROJECT\Semantic_Search_Assistant\test_docs\hi.txt
2025-07-18 22:09:54,451 - document_processor - INFO - Processed hi.txt: 1 chunks created
2025-07-18 22:09:54,451 - main - INFO - Processed E:\PROJECT\Semantic_Search_Assistant\test_docs\hi.txt: 1 chunks, type: <class 'list'>
2025-07-18 22:09:54,451 - main - INFO - First chunk type: <class 'document_processor.DocumentChunk'>, has content: True
2025-07-18 22:09:54,451 - database - INFO - add_document called with 1 chunks, type: <class 'list'>
2025-07-18 22:09:54,452 - database - INFO - First chunk type: <class 'document_processor.DocumentChunk'>
2025-07-18 22:09:54,482 - database - INFO - Processing chunk 0: metadata type = <class 'dict'>, metadata = {'source': 'E:\\PROJECT\\Semantic_Search_Assistant\\test_docs\\hi.txt', 'filename': 'hi.txt', 'extension': '.txt', 'file_size': 28, 'modified_time': 1751918300.4052818, 'format': 'text', 'lines': 1, 'words': 5, 'chunk_index': 0, 'total_chunks': 1, 'chunk_size': 28}
2025-07-18 22:09:54,501 - database - INFO - Added 1 chunks for document: hi.txt
2025-07-18 22:09:54,502 - folder_manager - INFO - Successfully processed: E:\PROJECT\Semantic_Search_Assistant\test_docs\hi.txt (0 chunks)
2025-07-18 22:09:54,503 - folder_manager - INFO - Processing file: E:\PROJECT\Semantic_Search_Assistant\test_docs\realtime_test.txt
2025-07-18 22:09:54,504 - main - INFO - Processing document 1/1: E:\PROJECT\Semantic_Search_Assistant\test_docs\realtime_test.txt
2025-07-18 22:09:54,505 - document_processor - INFO - Processed realtime_test.txt: 2 chunks created
2025-07-18 22:09:54,506 - main - INFO - Processed E:\PROJECT\Semantic_Search_Assistant\test_docs\realtime_test.txt: 2 chunks, type: <class 'list'>
2025-07-18 22:09:54,506 - main - INFO - First chunk type: <class 'document_processor.DocumentChunk'>, has content: True
2025-07-18 22:09:54,506 - database - INFO - add_document called with 2 chunks, type: <class 'list'>
2025-07-18 22:09:54,506 - database - INFO - First chunk type: <class 'document_processor.DocumentChunk'>
2025-07-18 22:09:54,623 - database - INFO - Processing chunk 0: metadata type = <class 'dict'>, metadata = {'source': 'E:\\PROJECT\\Semantic_Search_Assistant\\test_docs\\realtime_test.txt', 'filename': 'realtime_test.txt', 'extension': '.txt', 'file_size': 868, 'modified_time': 1752853555.3566787, 'format': 'text', 'lines': 27, 'words': 119, 'chunk_index': 0, 'total_chunks': 2, 'chunk_size': 471}
2025-07-18 22:09:54,624 - database - INFO - Processing chunk 1: metadata type = <class 'dict'>, metadata = {'source': 'E:\\PROJECT\\Semantic_Search_Assistant\\test_docs\\realtime_test.txt', 'filename': 'realtime_test.txt', 'extension': '.txt', 'file_size': 868, 'modified_time': 1752853555.3566787, 'format': 'text', 'lines': 27, 'words': 119, 'chunk_index': 1, 'total_chunks': 2, 'chunk_size': 367}
2025-07-18 22:09:54,636 - database - INFO - Added 2 chunks for document: realtime_test.txt
2025-07-18 22:09:54,637 - folder_manager - INFO - Successfully processed: E:\PROJECT\Semantic_Search_Assistant\test_docs\realtime_test.txt (0 chunks)
2025-07-18 22:09:54,639 - folder_manager - INFO - Processing file: E:\PROJECT\Semantic_Search_Assistant\test_docs\test_indexing.txt
2025-07-18 22:09:54,639 - main - INFO - Processing document 1/1: E:\PROJECT\Semantic_Search_Assistant\test_docs\test_indexing.txt
2025-07-18 22:09:54,640 - document_processor - INFO - Processed test_indexing.txt: 1 chunks created
2025-07-18 22:09:54,640 - main - INFO - Processed E:\PROJECT\Semantic_Search_Assistant\test_docs\test_indexing.txt: 1 chunks, type: <class 'list'>
2025-07-18 22:09:54,640 - main - INFO - First chunk type: <class 'document_processor.DocumentChunk'>, has content: True
2025-07-18 22:09:54,641 - database - INFO - add_document called with 1 chunks, type: <class 'list'>
2025-07-18 22:09:54,641 - database - INFO - First chunk type: <class 'document_processor.DocumentChunk'>
2025-07-18 22:09:54,738 - database - INFO - Processing chunk 0: metadata type = <class 'dict'>, metadata = {'source': 'E:\\PROJECT\\Semantic_Search_Assistant\\test_docs\\test_indexing.txt', 'filename': 'test_indexing.txt', 'extension': '.txt', 'file_size': 420, 'modified_time': 1752519686.0287697, 'format': 'text', 'lines': 12, 'words': 65, 'chunk_index': 0, 'total_chunks': 1, 'chunk_size': 419}
2025-07-18 22:09:54,753 - database - INFO - Added 1 chunks for document: test_indexing.txt
2025-07-18 22:09:54,753 - folder_manager - INFO - Successfully processed: E:\PROJECT\Semantic_Search_Assistant\test_docs\test_indexing.txt (0 chunks)
2025-07-18 22:09:54,754 - folder_manager - INFO - Processing file: E:\PROJECT\Semantic_Search_Assistant\test_docs\wasay.txt
2025-07-18 22:09:54,754 - main - INFO - Processing document 1/1: E:\PROJECT\Semantic_Search_Assistant\test_docs\wasay.txt
2025-07-18 22:09:54,757 - document_processor - INFO - Processed wasay.txt: 1 chunks created
2025-07-18 22:09:54,757 - main - INFO - Processed E:\PROJECT\Semantic_Search_Assistant\test_docs\wasay.txt: 1 chunks, type: <class 'list'>
2025-07-18 22:09:54,757 - main - INFO - First chunk type: <class 'document_processor.DocumentChunk'>, has content: True
2025-07-18 22:09:54,757 - database - INFO - add_document called with 1 chunks, type: <class 'list'>
2025-07-18 22:09:54,757 - database - INFO - First chunk type: <class 'document_processor.DocumentChunk'>
2025-07-18 22:09:54,802 - database - INFO - Processing chunk 0: metadata type = <class 'dict'>, metadata = {'source': 'E:\\PROJECT\\Semantic_Search_Assistant\\test_docs\\wasay.txt', 'filename': 'wasay.txt', 'extension': '.txt', 'file_size': 15, 'modified_time': 1752519906.9360347, 'format': 'text', 'lines': 1, 'words': 4, 'chunk_index': 0, 'total_chunks': 1, 'chunk_size': 15}
2025-07-18 22:09:54,828 - database - INFO - Added 1 chunks for document: wasay.txt
2025-07-18 22:09:54,833 - folder_manager - INFO - Successfully processed: E:\PROJECT\Semantic_Search_Assistant\test_docs\wasay.txt (0 chunks)
2025-07-18 22:10:16,510 - main - INFO - Search for 'test' returned 4 results
2025-07-18 22:17:09,085 - __main__ - INFO - Starting Semantic Search Assistant backend...
2025-07-18 22:17:18,949 - folder_manager - INFO - Loaded 2 connected folders
2025-07-18 22:17:18,957 - main - INFO - Initializing document search backend...
2025-07-18 22:17:18,957 - database - INFO - Loading embedding model: sentence-transformers/all-MiniLM-L6-v2
2025-07-18 22:17:18,959 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-18 22:17:18,959 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
2025-07-18 22:17:24,679 - database - INFO - Connected to existing table: documents
2025-07-18 22:17:24,680 - database - INFO - Vector store initialized successfully
2025-07-18 22:17:24,680 - document_processor - INFO - Document processor initialized
2025-07-18 22:17:24,786 - main - INFO - Added test_docs folder to monitoring: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-18 22:17:24,786 - folder_manager - INFO - Starting folder monitoring...
2025-07-18 22:17:24,787 - folder_manager - INFO - Started monitoring folder: E:\PROJECT\Semantic_Search_Assistant\test_docs
2025-07-18 22:17:24,787 - folder_manager - WARNING - Folder does not exist, skipping monitoring: C:\Users\<USER>\OneDrive\Desktop\BilalProject\Semantic_Search_Assistant\test_docs
2025-07-18 22:17:24,788 - folder_manager - INFO - Monitoring 2 folders
2025-07-18 22:17:24,794 - api_service - INFO - API server started successfully
2025-07-18 22:17:24,806 - folder_manager - INFO - Starting background document processor...
2025-07-18 22:18:13,600 - main - INFO - Search for 'machine learning' returned 3 results
2025-07-18 22:18:23,656 - main - INFO - Search for 'neural networks' returned 5 results
2025-07-18 22:18:42,726 - main - INFO - Search for 'supervised learning' returned 5 results
2025-07-18 22:22:49,845 - __main__ - INFO - Starting Semantic Search Assistant backend...
