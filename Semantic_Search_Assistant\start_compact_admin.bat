@echo off
echo ========================================
echo  Compact Side Window Search Monitor
echo ========================================
echo.
echo This will start a small, compact window
echo on the right side of your screen for
echo real-time search monitoring.
echo.
echo Features:
echo - Small side window (380x600)
echo - Always stays on top
echo - Positioned on right side
echo - Real-time global monitoring
echo - Instant search results
echo - Copy functionality
echo.
echo ========================================
echo.

:: Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running as Administrator - Perfect!
    echo.
    python start_compact_side.py
) else (
    echo Requesting Administrator privileges...
    echo.
    powershell -Command "Start-Process cmd -ArgumentList '/c cd /d %cd% && python start_compact_side.py && pause' -Verb RunAs"
)

pause
