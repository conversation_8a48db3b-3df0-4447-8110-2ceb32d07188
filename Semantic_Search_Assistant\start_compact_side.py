#!/usr/bin/env python3
"""
Launcher for Compact Side Window Real-time Search Monitor
"""

import sys
import ctypes
from pathlib import Path

# Add current directory to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def is_admin():
    """Check if running as administrator."""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def main():
    print("🔍 Compact Side Window Real-time Search Monitor")
    print("=" * 60)
    print("✨ Perfect for side-by-side use!")
    print()
    print("🎯 Features:")
    print("  • Small window on right side of screen")
    print("  • Always stays on top")
    print("  • Compact, efficient design")
    print("  • Real-time global monitoring")
    print("  • Instant search with copy functionality")
    print()
    
    if not is_admin():
        print("⚠️  NOT running as Administrator")
        print("   For best global monitoring, run as Administrator:")
        print("   Right-click start_compact_admin.bat → 'Run as administrator'")
        print()
    else:
        print("✅ Running as Administrator - Perfect!")
        print()
    
    print("=" * 60)
    print()
    
    try:
        from compact_side_monitor import main as app_main
        app_main()
    except Exception as e:
        print(f"❌ Error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
