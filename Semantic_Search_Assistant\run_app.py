#!/usr/bin/env python3
"""
Lightweight Semantic Search Assistant with Real-time Text Suggestions.
Works immediately without heavy ML backend.
"""

import os
import sys
import time
import signal
import logging
import threading
from pathlib import Path

# Add current directory to path
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LightweightSearchBackend:
    """Lightweight search backend using document files directly."""
    
    def __init__(self, docs_folder):
        self.docs_folder = Path(docs_folder)
        self.documents = []
        self._load_documents()
    
    def _load_documents(self):
        """Load documents from test_docs folder."""
        if not self.docs_folder.exists():
            return
        
        for file_path in self.docs_folder.glob("*.txt"):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    self.documents.append({
                        "content": content,
                        "source": file_path.name,
                        "similarity": 0.8
                    })
            except Exception as e:
                logger.error(f"Error loading {file_path}: {e}")
    
    def search(self, query):
        """Simple text-based search."""
        if not query or len(query.strip()) < 2:
            return []
        
        query_lower = query.lower()
        results = []
        
        for doc in self.documents:
            content_lower = doc["content"].lower()
            
            # Simple keyword matching
            if query_lower in content_lower:
                # Calculate simple relevance score
                words = query_lower.split()
                matches = sum(1 for word in words if word in content_lower)
                relevance = (matches / len(words)) * doc["similarity"]
                
                if relevance > 0.5:  # Only show >50% relevance
                    result = doc.copy()
                    result["similarity"] = relevance
                    results.append(result)
        
        # Sort by relevance
        results.sort(key=lambda x: x["similarity"], reverse=True)
        return results[:6]

class SemanticSearchApp:
    """Lightweight Semantic Search Assistant Application."""
    
    def __init__(self):
        self.current_dir = Path(__file__).parent.absolute()
        self.monitor_thread = None
        self.is_running = False
        self.test_docs_folder = self.current_dir / "test_docs"
        self.backend = LightweightSearchBackend(self.test_docs_folder)
        
    def run(self):
        """Run the lightweight application."""
        try:
            print("🚀 Semantic Search Assistant")
            print("   Real-time Text Suggestion App")
            print("=" * 50)
            print("⚡ Lightweight version - starts instantly!")
            
            # Setup signal handlers
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            
            # Load documents
            print(f"📚 Loading documents from {self.test_docs_folder}...")
            doc_count = len(self.backend.documents)
            if doc_count > 0:
                print(f"✅ Loaded {doc_count} documents")
            else:
                print("⚠️  No documents found in test_docs folder")
                print("💡 Add .txt files to test_docs folder for better suggestions")
            
            # Start real-time monitoring
            if not self._start_realtime_monitoring():
                print("❌ Failed to start real-time monitoring")
                return False
            
            self.is_running = True
            self._display_status()
            self._wait_for_shutdown()
            
            return True
            
        except Exception as e:
            logger.error(f"Application error: {e}")
            return False
        finally:
            self._shutdown()
    
    def _start_realtime_monitoring(self):
        """Start real-time text monitoring."""
        print("👁️  Starting real-time monitoring...")
        
        try:
            def run_monitor():
                try:
                    from enhanced_realtime_monitor import EnhancedRealtimeMonitor, FloatingSuggestionUI
                    
                    # Create UI
                    ui = FloatingSuggestionUI()
                    
                    def on_suggestions(query, suggestions):
                        if query:
                            # Get suggestions from lightweight backend
                            results = self.backend.search(query)
                            ui.update_suggestions(query, results)
                        else:
                            ui.update_suggestions("", [])
                    
                    # Create and start monitor
                    monitor = EnhancedRealtimeMonitor(on_suggestions)
                    monitor.start_monitoring()
                    
                    print("✅ Real-time monitoring active")
                    print("📝 Open any text editor and start typing!")
                    
                    # Run UI
                    ui.run()
                    
                except Exception as e:
                    logger.error(f"Error in real-time monitoring: {e}")
            
            self.monitor_thread = threading.Thread(target=run_monitor, daemon=True)
            self.monitor_thread.start()
            
            # Give the thread a moment to start
            time.sleep(1)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to start monitoring: {e}")
            return False
    
    def _display_status(self):
        """Display application status."""
        print("\n" + "=" * 50)
        print("🎉 Semantic Search Assistant is running!")
        print("=" * 50)
        print(f"📁 Documents: {len(self.backend.documents)} loaded")
        print("👁️  Real-time monitoring: Active")
        print("⚡ Lightweight mode: Instant startup")
        print("\n💡 How to use:")
        print("  1. Open any text editor (Word, Notepad, VS Code, etc.)")
        print("  2. Start typing any word")
        print("  3. Watch suggestions appear for each letter")
        print("  4. Press spacebar to clear search for next word")
        print("  5. Double-click suggestions to copy them")
        print("\n⏹️  Press Ctrl+C to stop")
        print("=" * 50)
    
    def _wait_for_shutdown(self):
        """Wait for shutdown signal."""
        try:
            while self.is_running:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Shutdown requested...")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        self.is_running = False
    
    def _shutdown(self):
        """Shutdown all components."""
        print("🔄 Shutting down...")
        print("✅ Real-time monitoring stopped")
        print("👋 Goodbye!")

def main():
    """Main entry point."""
    app = SemanticSearchApp()
    success = app.run()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
