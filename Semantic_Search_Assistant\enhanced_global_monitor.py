#!/usr/bin/env python3
"""
Enhanced Global Keyboard Monitor with Multiple Detection Methods
Ensures typing detection works with Word and all applications.
"""

import threading
import time
import logging
import sys
import os
import subprocess
import ctypes
from pathlib import Path
from typing import List, Dict, Any
import tkinter as tk
from tkinter import ttk, messagebox
import requests

# Try multiple monitoring approaches
try:
    import keyboard
    KEYBOARD_AVAILABLE = True
except ImportError:
    KEYBOARD_AVAILABLE = False

try:
    import pyperclip
    CLIPBOARD_AVAILABLE = True
except ImportError:
    CLIPBOARD_AVAILABLE = False

try:
    import win32gui
    import win32con
    import win32api
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedGlobalMonitor:
    """Enhanced global monitor with multiple detection methods."""
    
    def __init__(self, callback):
        self.callback = callback
        self.is_monitoring = False
        self.current_word = ""
        self.last_clipboard = ""
        self.monitor_thread = None
        
    def start_monitoring(self):
        """Start monitoring with multiple methods."""
        if self.is_monitoring:
            return True
            
        # Check if running as administrator
        if not self._is_admin():
            messagebox.showwarning("Administrator Required", 
                                 "For best results with Word and other applications,\n"
                                 "please run this application as Administrator.\n\n"
                                 "Right-click the .bat file and select 'Run as administrator'")
        
        success = False
        
        # Method 1: Try keyboard monitoring
        if KEYBOARD_AVAILABLE:
            try:
                keyboard.on_press(self._on_key_press)
                self.is_monitoring = True
                success = True
                logger.info("✅ Keyboard monitoring started")
            except Exception as e:
                logger.error(f"❌ Keyboard monitoring failed: {e}")
        
        # Method 2: Start clipboard monitoring as backup
        if CLIPBOARD_AVAILABLE:
            self.monitor_thread = threading.Thread(target=self._clipboard_monitor, daemon=True)
            self.monitor_thread.start()
            logger.info("✅ Clipboard monitoring started as backup")
            success = True
        
        if success:
            logger.info("🌍 Global monitoring active")
            return True
        else:
            logger.error("❌ No monitoring methods available")
            return False
            
    def stop_monitoring(self):
        """Stop all monitoring."""
        self.is_monitoring = False
        
        if KEYBOARD_AVAILABLE:
            try:
                keyboard.unhook_all()
            except:
                pass
                
        logger.info("🛑 Global monitoring stopped")
        
    def _is_admin(self):
        """Check if running as administrator."""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
            
    def _on_key_press(self, key):
        """Handle keyboard events."""
        if not self.is_monitoring:
            return
            
        try:
            key_name = key.name if hasattr(key, 'name') else str(key)
            
            # Handle spacebar - clear search
            if key_name == 'space':
                if self.current_word.strip():
                    self.current_word = ""
                    self.callback("")
                return
                
            # Handle backspace
            elif key_name == 'backspace':
                if self.current_word:
                    self.current_word = self.current_word[:-1]
                    self.callback(self.current_word)
                return
                
            # Handle regular characters
            elif len(key_name) == 1 and key_name.isalnum():
                self.current_word += key_name
                self.callback(self.current_word)
                
        except Exception as e:
            logger.error(f"Key press error: {e}")
            
    def _clipboard_monitor(self):
        """Monitor clipboard for text changes (backup method)."""
        while self.is_monitoring:
            try:
                if CLIPBOARD_AVAILABLE:
                    current_clipboard = pyperclip.paste()
                    
                    # If clipboard changed and contains new text
                    if (current_clipboard != self.last_clipboard and 
                        len(current_clipboard) > len(self.last_clipboard) and
                        len(current_clipboard) < 100):  # Reasonable word length
                        
                        # Extract the new part
                        if self.last_clipboard and current_clipboard.startswith(self.last_clipboard):
                            new_text = current_clipboard[len(self.last_clipboard):].strip()
                            if new_text and len(new_text) < 20:  # Single word
                                self.current_word = new_text
                                self.callback(new_text)
                        
                        self.last_clipboard = current_clipboard
                        
            except Exception as e:
                logger.error(f"Clipboard monitor error: {e}")
                
            time.sleep(0.5)

class SimpleSearchAPI:
    """Simple search API client."""
    
    def __init__(self):
        self.base_url = "http://127.0.0.1:8000"
        
    def search(self, query: str) -> List[Dict[str, Any]]:
        """Search for query."""
        if not query.strip():
            return []
            
        try:
            response = requests.post(
                f"{self.base_url}/search",
                json={"query": query, "limit": 8, "similarity_threshold": 0.3},
                timeout=2
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get("results", [])
            return []
        except:
            return []
            
    def check_backend(self) -> bool:
        """Check if backend is running."""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=2)
            return response.status_code == 200
        except:
            return False

class EnhancedGlobalApp:
    """Enhanced GUI with better monitoring."""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Enhanced Global Real-time Search Monitor")
        self.root.geometry("1100x800")
        self.root.configure(bg='#f0f0f0')
        
        # Make window always on top for better visibility
        self.root.attributes('-topmost', True)
        
        # Components
        self.monitor = EnhancedGlobalMonitor(self.on_text_detected)
        self.search_api = SimpleSearchAPI()
        
        # State
        self.is_monitoring = False
        self.current_query = ""
        self.search_results = []
        
        # Create GUI
        self.create_widgets()
        
        # Check backend
        self.check_backend()
        
        # Bind close
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def create_widgets(self):
        """Create GUI widgets."""
        # Main frame
        main_frame = ttk.Frame(self.root, padding=15)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="🌍 Enhanced Global Real-time Search Monitor", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 5))
        
        subtitle_label = ttk.Label(main_frame, 
                                  text="Type in Word, Notepad, VS Code → Watch text appear here → Get suggestions", 
                                  font=('Arial', 10), foreground='#666')
        subtitle_label.pack(pady=(0, 15))
        
        # Admin warning
        if not self.monitor._is_admin():
            warning_frame = ttk.Frame(main_frame)
            warning_frame.pack(fill=tk.X, pady=(0, 10))
            
            warning_label = ttk.Label(warning_frame, 
                                     text="⚠️ For best results with Word: Run as Administrator", 
                                     font=('Arial', 10, 'bold'), foreground='orange')
            warning_label.pack()
        
        # Status frame
        status_frame = ttk.LabelFrame(main_frame, text="System Status", padding=10)
        status_frame.pack(fill=tk.X, pady=(0, 15))
        
        # Backend status
        backend_frame = ttk.Frame(status_frame)
        backend_frame.pack(fill=tk.X, pady=(0, 5))
        ttk.Label(backend_frame, text="Backend:", font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.backend_status = ttk.Label(backend_frame, text="Checking...", font=('Arial', 10))
        self.backend_status.pack(side=tk.LEFT, padx=(10, 0))
        
        # Monitor status
        monitor_frame = ttk.Frame(status_frame)
        monitor_frame.pack(fill=tk.X)
        ttk.Label(monitor_frame, text="Global Monitor:", font=('Arial', 10, 'bold')).pack(side=tk.LEFT)
        self.monitor_status = ttk.Label(monitor_frame, text="Stopped", font=('Arial', 10), foreground='red')
        self.monitor_status.pack(side=tk.LEFT, padx=(10, 0))
        
        # Control buttons
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.start_backend_btn = ttk.Button(control_frame, text="🚀 Start Backend", 
                                           command=self.start_backend)
        self.start_backend_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.start_monitor_btn = ttk.Button(control_frame, text="🌍 Start Global Monitor", 
                                           command=self.toggle_monitoring, state="disabled")
        self.start_monitor_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.test_btn = ttk.Button(control_frame, text="🧪 Test Typing", 
                                  command=self.test_typing)
        self.test_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Manual input for testing
        manual_frame = ttk.LabelFrame(main_frame, text="Manual Test (if global monitoring doesn't work)", padding=10)
        manual_frame.pack(fill=tk.X, pady=(0, 15))
        
        manual_input_frame = ttk.Frame(manual_frame)
        manual_input_frame.pack(fill=tk.X)
        
        ttk.Label(manual_input_frame, text="Type here to test:").pack(side=tk.LEFT)
        self.manual_entry = ttk.Entry(manual_input_frame, font=('Arial', 12))
        self.manual_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 0))
        self.manual_entry.bind('<KeyRelease>', self.on_manual_input)
        
        # Current typing display
        typing_frame = ttk.LabelFrame(main_frame, text="Live Typing Detection", padding=10)
        typing_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(typing_frame, text="Current Word:", font=('Arial', 11, 'bold')).pack(anchor=tk.W)
        self.current_word_display = ttk.Label(typing_frame, text="(not monitoring)", 
                                             font=('Arial', 16), foreground='gray',
                                             background='white', relief='sunken', padding=10)
        self.current_word_display.pack(fill=tk.X, pady=(5, 0))
        
        # Instructions
        instructions = """
🎯 How to use:
1. Click "Start Backend" → 2. Click "Start Global Monitor" → 3. Type in Word/Notepad!

📝 Testing steps:
• Open Microsoft Word or Notepad
• Start typing a word (e.g., "machine")
• Watch it appear above in real-time
• Press SPACEBAR to clear and start new word

⚠️ If global monitoring doesn't work:
• Try running as Administrator
• Use the manual test box above
• Check that keyboard/pyperclip are installed
        """
        ttk.Label(typing_frame, text=instructions, font=('Arial', 9), 
                 justify=tk.LEFT, foreground='#555').pack(anchor=tk.W, pady=(10, 0))
        
        # Search results
        results_frame = ttk.LabelFrame(main_frame, text="Live Search Results", padding=10)
        results_frame.pack(fill=tk.BOTH, expand=True)
        
        # Results text widget
        self.results_text = tk.Text(results_frame, font=('Arial', 10), wrap=tk.WORD, height=12)
        self.results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.results_text.configure(yscrollcommand=scrollbar.set)
        
        # Initial message
        self.results_text.insert(tk.END, "🔍 Start global monitoring and type in Word or Notepad!\n\n")
        self.results_text.insert(tk.END, "💡 Supported applications:\n")
        self.results_text.insert(tk.END, "• Microsoft Word, WordPad, Notepad\n")
        self.results_text.insert(tk.END, "• VS Code, Sublime Text, Atom\n")
        self.results_text.insert(tk.END, "• Any text editor or input field\n\n")
        self.results_text.insert(tk.END, "🔧 Troubleshooting:\n")
        self.results_text.insert(tk.END, "• Run as Administrator for best results\n")
        self.results_text.insert(tk.END, "• Try the manual test box if global monitoring fails\n")
        self.results_text.insert(tk.END, "• Make sure to add documents via web interface first\n")
        
    def check_backend(self):
        """Check backend status."""
        if self.search_api.check_backend():
            self.backend_status.config(text="✅ Running", foreground="green")
            self.start_backend_btn.config(text="✅ Backend Running")
            self.start_monitor_btn.config(state="normal")
        else:
            self.backend_status.config(text="❌ Not Running", foreground="red")
            
    def start_backend(self):
        """Start backend."""
        self.start_backend_btn.config(text="Starting...", state="disabled")
        self.backend_status.config(text="🔄 Starting...", foreground="orange")
        
        threading.Thread(target=self._start_backend_process, daemon=True).start()
        
    def _start_backend_process(self):
        """Start backend process."""
        try:
            subprocess.Popen([sys.executable, "start_backend.py"], cwd=Path(__file__).parent)
            
            for i in range(30):
                time.sleep(1)
                if self.search_api.check_backend():
                    self.root.after(0, self.on_backend_started)
                    return
                    
            self.root.after(0, self.on_backend_failed)
            
        except Exception as e:
            logger.error(f"Failed to start backend: {e}")
            self.root.after(0, self.on_backend_failed)
            
    def on_backend_started(self):
        """Backend started successfully."""
        self.backend_status.config(text="✅ Running", foreground="green")
        self.start_backend_btn.config(text="✅ Backend Running", state="normal")
        self.start_monitor_btn.config(state="normal")
        messagebox.showinfo("Success", "Backend started!\n\nNext steps:\n1. Click 'Start Global Monitor'\n2. Open Word/Notepad and start typing!")
        
    def on_backend_failed(self):
        """Backend failed to start."""
        self.backend_status.config(text="❌ Failed", foreground="red")
        self.start_backend_btn.config(text="🚀 Start Backend", state="normal")
        messagebox.showerror("Error", "Backend failed to start.\nCheck console for errors.")
        
    def toggle_monitoring(self):
        """Toggle global monitoring."""
        if not self.is_monitoring:
            success = self.monitor.start_monitoring()
            if success:
                self.is_monitoring = True
                self.start_monitor_btn.config(text="🛑 Stop Monitor")
                self.monitor_status.config(text="✅ Active", foreground="green")
                self.current_word_display.config(text="Ready! Type in Word/Notepad...", foreground="blue")
                
                messagebox.showinfo("Global Monitor Started", 
                                  "🌍 Global monitoring is now active!\n\n"
                                  "✨ Now type in:\n"
                                  "• Microsoft Word\n"
                                  "• Notepad\n"
                                  "• VS Code\n"
                                  "• Any text editor\n\n"
                                  "Watch your typing appear in this window!\n\n"
                                  "⚠️ If it doesn't work, try:\n"
                                  "• Running as Administrator\n"
                                  "• Using the manual test box")
            else:
                messagebox.showerror("Error", "Failed to start global monitoring.\n\nTry:\n• Running as Administrator\n• Installing: pip install keyboard pyperclip")
        else:
            self.monitor.stop_monitoring()
            self.is_monitoring = False
            self.start_monitor_btn.config(text="🌍 Start Global Monitor")
            self.monitor_status.config(text="❌ Stopped", foreground="red")
            self.current_word_display.config(text="(not monitoring)", foreground="gray")
            
    def test_typing(self):
        """Test the typing detection."""
        messagebox.showinfo("Test Instructions", 
                          "🧪 Testing Global Monitoring:\n\n"
                          "1. Make sure 'Start Global Monitor' is clicked\n"
                          "2. Open Microsoft Word or Notepad\n"
                          "3. Type the word 'test' slowly\n"
                          "4. Watch the 'Current Word' display above\n"
                          "5. Press SPACEBAR to clear\n\n"
                          "If nothing appears:\n"
                          "• Try running as Administrator\n"
                          "• Use the manual test box above")
        
    def on_manual_input(self, event):
        """Handle manual input for testing."""
        text = self.manual_entry.get()
        if text:
            # Simulate the global monitoring
            self.on_text_detected(text)
        
    def on_text_detected(self, text: str):
        """Handle text detected from monitoring."""
        self.current_query = text
        
        # Update display
        if text:
            self.current_word_display.config(text=f"'{text}'", foreground="blue")
        else:
            self.current_word_display.config(text="(cleared - ready for next word)", foreground="green")
            self.results_text.delete(1.0, tk.END)
            self.results_text.insert(tk.END, "Search cleared. Start typing for new search...\n")
            return
            
        # Search
        if len(text) >= 1:
            threading.Thread(target=self._search_background, args=(text,), daemon=True).start()
            
    def _search_background(self, query: str):
        """Search in background."""
        try:
            results = self.search_api.search(query)
            self.root.after(0, lambda: self._update_results(query, results))
        except Exception as e:
            logger.error(f"Search error: {e}")
            
    def _update_results(self, query: str, results: List[Dict[str, Any]]):
        """Update search results."""
        if query != self.current_query:
            return
            
        self.search_results = results
        self.results_text.delete(1.0, tk.END)
        
        if not results:
            self.results_text.insert(tk.END, f"🔍 Searching for: '{query}'\n\n")
            self.results_text.insert(tk.END, "No results found.\n\n")
            self.results_text.insert(tk.END, "💡 To get results:\n")
            self.results_text.insert(tk.END, "1. Add documents via web interface:\n")
            self.results_text.insert(tk.END, "   http://127.0.0.1:8000/static/app.html\n")
            self.results_text.insert(tk.END, "2. Or add some test documents to test_docs/ folder\n")
            return
            
        self.results_text.insert(tk.END, f"🔍 Live search for: '{query}' ({len(results)} results)\n")
        self.results_text.insert(tk.END, "=" * 50 + "\n\n")
        
        for i, result in enumerate(results, 1):
            content = result.get('content', '')
            source = result.get('source', 'Unknown')
            similarity = result.get('similarity', 0) * 100
            
            self.results_text.insert(tk.END, f"{i}. [{similarity:.1f}%] ")
            self.results_text.insert(tk.END, f"{content[:120]}...\n")
            self.results_text.insert(tk.END, f"   📄 {source}\n\n")
            
        self.results_text.insert(tk.END, "💡 Select any text above and Ctrl+C to copy\n")
        self.results_text.see(1.0)
        
    def on_closing(self):
        """Handle closing."""
        if self.is_monitoring:
            self.monitor.stop_monitoring()
        self.root.destroy()
        
    def run(self):
        """Run the app."""
        self.root.mainloop()

def main():
    """Main entry point."""
    print("🌍 Enhanced Global Real-time Search Monitor")
    print("=" * 50)
    print("🎯 Type in Word/Notepad and see it here!")
    print()
    
    # Check dependencies
    missing = []
    if not KEYBOARD_AVAILABLE:
        missing.append("keyboard")
    if not CLIPBOARD_AVAILABLE:
        missing.append("pyperclip")
        
    if missing:
        print(f"⚠️  Install missing packages: pip install {' '.join(missing)}")
        print()
    
    try:
        app = EnhancedGlobalApp()
        app.run()
    except Exception as e:
        print(f"Error: {e}")
        messagebox.showerror("Error", f"Application error: {e}")

if __name__ == "__main__":
    main()
